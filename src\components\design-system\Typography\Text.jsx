// src/components/design-system/Typography/Text.jsx
import React from 'react';
import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledText = styled(Typography, {
  shouldForwardProp: (prop) => !['size', 'weight', 'variant'].includes(prop),
})(({ theme, size, weight, color, variant }) => ({
  fontWeight: weight || 'inherit',
  color: color || 'inherit',

  // Size variants
  ...(size === 'xs' && {
    fontSize: theme.typography.fontSize?.xs || '0.75rem',
    lineHeight: theme.typography.lineHeight?.normal || 1.5,
  }),
  ...(size === 'sm' && {
    fontSize: theme.typography.fontSize?.sm || '0.875rem',
    lineHeight: theme.typography.lineHeight?.normal || 1.5,
  }),
  ...(size === 'base' && {
    fontSize: theme.typography.fontSize?.base || '1rem',
    lineHeight: theme.typography.lineHeight?.relaxed || 1.6,
  }),
  ...(size === 'lg' && {
    fontSize: theme.typography.fontSize?.lg || '1.125rem',
    lineHeight: theme.typography.lineHeight?.relaxed || 1.6,
  }),
  ...(size === 'xl' && {
    fontSize: theme.typography.fontSize?.xl || '1.25rem',
    lineHeight: theme.typography.lineHeight?.relaxed || 1.6,
  }),

  // Variant styles
  ...(variant === 'muted' && {
    color: theme.palette.text.secondary,
  }),
  ...(variant === 'subtle' && {
    color: theme.palette.text.disabled,
  }),
  ...(variant === 'error' && {
    color: theme.palette.error.main,
  }),
  ...(variant === 'success' && {
    color: theme.palette.success.main,
  }),
  ...(variant === 'warning' && {
    color: theme.palette.warning.main,
  }),
}));

/**
 * Text component for consistent body text styling
 *
 * @param {Object} props
 * @param {'xs'|'sm'|'base'|'lg'|'xl'} props.size - Text size
 * @param {number} props.weight - Font weight
 * @param {string} props.color - Text color
 * @param {'muted'|'subtle'|'error'|'success'|'warning'} props.variant - Text variant
 * @param {'p'|'span'|'div'} props.as - HTML element to render
 * @param {React.ReactNode} props.children - Text content
 */
const Text = React.forwardRef(({
  size = 'base',
  weight,
  color,
  variant,
  as = 'p',
  children,
  ...props
}, ref) => {
  return (
    <StyledText
      ref={ref}
      component={as}
      size={size}
      weight={weight}
      color={color}
      variant={variant}
      {...props}
    >
      {children}
    </StyledText>
  );
});

Text.displayName = 'Text';

export default Text;
