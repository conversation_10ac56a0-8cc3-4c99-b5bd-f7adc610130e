// src/components/AIGenerationLoader.jsx
import React, { useRef, useLayoutEffect } from 'react';
import { gsap } from 'gsap';
import './AIGenerationLoader.css'; // Import the specific CSS

// --- Configuration ---
const N_CUBES = 15; // Reduced number slightly for performance/aesthetics
const FACE_Z = 150; // Cube face depth
const CUBE_HEIGHT = 50; // Match CSS
const CUBE_BOTTOM_PADDING = 8; // Match CSS

// Face rotation setup
const FACE_ROTS = [
    { ry: 270, a: 0.4 }, // Back (-Z equivalent when rotated) - dimmer
    { ry: 0, a: 0.9 },   // Front (+Z) - brightest
    { ry: 90, a: 0.6 },  // Right (+X) - medium
    { ry: 180, a: 0.1 }, // Left (-X) - darkest (Used for color calc range only)
];

function AIGenerationLoader() {
    const povRef = useRef(null);
    const trayRef = useRef(null);
    const templateDieRef = useRef(null); // Ref to the template die in JSX
    const timelinesRef = useRef([]);
    const mainTimelineRef = useRef(null);
    // Use a ref to track if initial setup is done
    const isSetupDone = useRef(false);

    useLayoutEffect(() => {
        // Context for GSAP selector performance
        const ctx = gsap.context(() => {
            const trayElement = trayRef.current;
            const dieTemplateElement = templateDieRef.current; // Template die
            const povElement = povRef.current;

            if (!trayElement || !dieTemplateElement || !povElement || isSetupDone.current) {
                 console.log("Loader effect: Skipping setup (missing refs or already done)");
                 return; // Skip if refs not ready or setup already ran
            }
            console.log("Loader effect: Starting setup...");

             // --- Cleanup potential previous state (important for hot-reloading) ---
             timelinesRef.current.forEach(tl => tl.kill());
             timelinesRef.current = [];
             if (mainTimelineRef.current) mainTimelineRef.current.kill();
             const clones = trayElement.querySelectorAll('.aigen-loader-die:not([data-template="true"])');
             clones.forEach(clone => clone.remove());
             // Reset specific styles affected by GSAP if needed
              gsap.set(trayElement.querySelectorAll('.aigen-loader-die'), { opacity: 0 }); // Reset opacity for stagger


            // --- Initial Setup for Template Faces ---
            gsap.set(dieTemplateElement.querySelectorAll('.aigen-loader-face'), {
                z: FACE_Z,
                rotateY: (i) => FACE_ROTS[i]?.ry ?? 0,
                transformOrigin: `50% 50% -${FACE_Z + 1}px` // Match Z depth
            });

            // --- Clone and Animate ---
            for (let i = 0; i < N_CUBES; i++) {
                let currentDie = dieTemplateElement;
                let currentCube = currentDie.querySelector('.aigen-loader-cube');

                if (i > 0) { // Clone for subsequent dies
                    let clone = dieTemplateElement.cloneNode(true);
                    clone.removeAttribute('data-template'); // Mark as not template
                    trayElement.append(clone);
                    currentDie = clone;
                    currentCube = currentDie.querySelector('.aigen-loader-cube');
                } else {
                    currentDie.removeAttribute('data-template'); // Remove attr from original too after using it
                     gsap.set(currentDie, { opacity: 0}); // Ensure template is also hidden initially
                }


                // Create timeline for this cube
                const tl = gsap.timeline({ repeat: -1, yoyo: true, defaults: { ease: 'power1.inOut', duration: 1.2 } }) // Adjusted ease/duration
                    .fromTo(currentCube, {
                        rotateY: -85 // Start slightly angled
                    }, {
                        rotateY: 85, // End slightly angled
                        ease: 'sine.inOut', // Smoother ease
                        duration: 2.4 // Match color transition?
                    })
                    // Face color animation synced with rotation
                    .fromTo(currentCube.querySelectorAll('.aigen-loader-face'), {
                        color: (j) => `hsl(${(i / N_CUBES * 85 + 190)}, 65%, ${100 * [FACE_ROTS[3].a, FACE_ROTS[0].a, FACE_ROTS[1].a][j]}%)` // Range start colors
                    }, {
                        color: (j) => `hsl(${(i / N_CUBES * 85 + 190)}, 65%, ${100 * [FACE_ROTS[0].a, FACE_ROTS[1].a, FACE_ROTS[2].a][j]}%)` // Mid colors
                    }, 0) // Start at time 0
                    .to(currentCube.querySelectorAll('.aigen-loader-face'), {
                        color: (j) => `hsl(${(i / N_CUBES * 85 + 190)}, 65%, ${100 * [FACE_ROTS[1].a, FACE_ROTS[2].a, FACE_ROTS[3].a][j]}%)` // Range end colors
                    }, 1.2) // End at half duration
                    .progress(i / N_CUBES); // Offset the start

                timelinesRef.current.push(tl); // Store timeline
            }

            // --- Main Tray Animation ---
             mainTimelineRef.current = gsap.timeline()
                .from(trayElement, { yPercent: -4, duration: 2.2, ease:'power1.inOut', yoyo: true, repeat: -1 }, 0)
                .fromTo(trayElement, { rotate: -8 }, { rotate: 8, duration: 4.5, ease:'sine.inOut', yoyo: true, repeat: -1 }, 0)
                // Staggered fade-in of ALL dies (template + clones)
                .to(trayElement.querySelectorAll('.aigen-loader-die'), { duration: 0.6, opacity: 1, stagger: { each: 0.04, ease:'power1.out' } }, 0.1) // Slightly delayed fade in
                 .to(trayElement, { scale: 1.05, duration: 2.2, ease:'power2.inOut', yoyo: true, repeat: -1 }, 0); // Subtle scale

             // Set height initially
              gsap.set(trayElement, { height: N_CUBES * (CUBE_HEIGHT + CUBE_BOTTOM_PADDING) });

              isSetupDone.current = true; // Mark setup as done
               console.log("Loader effect: Setup complete.");

        }, povRef); // Scope selectors to the POV container

        // --- Cleanup Function ---
        return () => {
            console.log("Loader effect: Cleaning up...");
             ctx.revert(); // Use GSAP context revert for cleanup
             timelinesRef.current = [];
             mainTimelineRef.current = null;
             isSetupDone.current = false; // Reset setup flag
             // No need to manually remove clones if context revert works well
        };

    }, []); // Empty dependency array ensures this runs once on mount

    return (
        // Main container for positioning and scaling
        <div ref={povRef} className="aigen-loader-pov">
             {/* Contains the stack of dies */}
            <div ref={trayRef} className="aigen-loader-tray">
                {/* TEMPLATE Die - Hidden initially, cloned by JS. data-template used for cleanup differentiation */}
                <div ref={templateDieRef} className="aigen-loader-die" data-template="true" style={{ opacity: 0 }}>
                    <div className="aigen-loader-cube">
                        {/* Keep keys for React */}
                        <div key="face-1" className="aigen-loader-face" style={{ fontSize: '24px' }}>PROCESSING</div>
                        <div key="face-2" className="aigen-loader-face" style={{ fontSize: '24px' }}>AI MAGIC</div>
                        <div key="face-3" className="aigen-loader-face" style={{ fontSize: '24px' }}>PLEASE WAIT</div>
                         {/* Hidden face needed for color calculations */}
                        <div key="face-4" className="aigen-loader-face" style={{ opacity: 0}}></div>
                    </div>
                </div>
                {/* Clones are added here by GSAP effect */}
            </div>
        </div>
    );
}

export default AIGenerationLoader;