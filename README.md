Nhost Migration & Enhancement Plan
This plan outlines the steps to migrate your React application from Firebase to Nhost and implement the requested Prompt and Credit/Pricing systems.
Phase 1: Nhost Migration (Replacing Firebase)
This phase focuses on swapping out Firebase services with Nhost equivalents while keeping the core application logic and the Cloudflare Worker's R2 interaction intact.
Nhost Environment Setup:
Local:
Install the Nhost CLI: npm i -g nhost-cli
Initialize Nhost in your project root: nhost init
Start the local Nhost backend (Docker): nhost dev. This spins up Postgres, Hasura GraphQL, Auth, and Storage containers.
Configure your frontend .env file with local Nhost URLs (e.g., VITE_NHOST_BACKEND_URL=http://localhost:1337/v1).
Cloud (Preparation):
Sign up for Nhost Cloud or prepare your chosen cloud provider (GCP/AWS) for self-hosting Nhost (using Docker Compose or Kubernetes).
Configure production environment variables later.
Authentication Migration (Firebase Auth -> Nhost Auth):
Dependencies:
Remove firebase, react-firebase-hooks.
Add Nhost React libraries: npm install @nhost/react @nhost/react-auth @nhost/react-apollo (Apollo for GraphQL).
Initialization:
Replace Firebase initialization (src/services/firebase.js) with Nhost client setup using @nhost/react. You'll need your Nhost backend URL.
Wrap your App component (or Main in src/main.jsx) with <NhostProvider>.
Auth Service (src/services/auth.js):
Replace signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut with Nhost equivalents: nhost.auth.signIn({ email, password }), nhost.auth.signUp({ email, password, options: { defaultRole: 'user', allowedRoles: ['user'], displayName: username } }), nhost.auth.signOut().
Update register to use Nhost sign-up, which handles user creation in the backend automatically (no separate setDoc needed here for basic user info if using Hasura default setup). Remove the Firestore setDoc call for initial user creation.
Rewrite getUserData to use a Hasura GraphQL query via @nhost/react-apollo or nhost.graphql.request to fetch data from the users table based on the authenticated user's ID.
UI Components:
Update src/App.jsx: Replace useAuthState with Nhost's useAuthenticationStatus and useUserId. Adjust protected route logic based on authentication status (isAuthenticated). Use useUserData from @nhost/react to get user profile data.
Update src/pages/Login.jsx and src/pages/Register.jsx: Call the updated Nhost auth functions from src/services/auth.js.
Update src/components/Navbar.jsx: Use Nhost hooks (useSignOut, useAuthenticationStatus) for auth state and logout actions.
Database Migration (Firestore -> Hasura GraphQL/Postgres):
Schema:
Using the Hasura Console (available via nhost dev), define tables mirroring your Firestore structure: users (Nhost creates this, add credits, username, plan_type, etc.), prompts, projects, comfyui_servers. Define relationships (e.g., user -> projects, user -> prompts).
Create Nhost database migrations to track schema changes (nhost migrations create ..., nhost up).
Permissions:
Configure row-level and column-level permissions in Hasura for each table (users, projects, prompts, comfyui_servers) to replicate your firestore.rules logic (e.g., users can only access their own projects/prompts).
Data Access:
Replace all Firestore SDK calls (getDocs, collection, where, doc, setDoc, addDoc, updateDoc, deleteDoc) with Hasura GraphQL queries and mutations.
Use @nhost/react-apollo hooks (useQuery, useMutation) or nhost.graphql.request for data fetching and manipulation.
Key Files to Update:
src/components/Projects.jsx: Fetch/delete projects using GraphQL.
src/pages/History.jsx: Fetch prompts using GraphQL.
src/pages/ProfileSettings.jsx: Update user profile (username) using a GraphQL mutation. Handle password updates via Nhost auth functions (nhost.auth.changePassword).
src/services/storageSystem.js:
getComfyInstanceUrl: Query the comfyui_servers table via GraphQL.
logPromptAttempt: Insert into the prompts table via GraphQL mutation.
saveProjectMetadata: Insert into the projects table via GraphQL mutation.
src/services/auth.js (getUserData): Query the users table via GraphQL.
Storage Migration (Firebase Storage -> Nhost Storage - for Workflows):
Upload Workflows: Upload your Imagen1.json, Videogen1.json, etc., to the Nhost Storage bucket using the Nhost console or CLI.
Permissions: Configure Nhost Storage access rules (likely requiring authentication) similar to your storage.rules.
Data Access (src/services/storageSystem.js):
Replace getDownloadURL from Firebase Storage with nhost.storage.getPresignedUrl({ fileId: '...' }) in the getWorkflowTemplate function. Use the File ID obtained after uploading the workflow to Nhost Storage.
API Worker Update (aigenius-api-worker/src/index.js):
Authentication:
Modify the verifyFirebaseToken function to verify Nhost JWTs passed in the Authorization: Bearer <token> header.
Use a standard JWT library (like jose or Cloudflare's crypto.subtle if available in the worker environment) along with Nhost's JWT secret (stored as a Worker secret: NHOST_JWT_SECRET). Nhost JWTs contain standard claims like sub (user ID) and Hasura claims (x-nhost-user-id, x-nhost-default-role, etc.). Verify the signature and issuer.
Environment Variables: Add NHOST_JWT_SECRET to wrangler.jsonc secrets configuration. Remove FIREBASE_PROJECT_ID.
Configuration & Cleanup:
Remove Firebase config files: firebase.json, firestore.rules, storage.rules.
Remove Firebase-related environment variables from .env.
Add Nhost environment variables: VITE_NHOST_SUBDOMAIN, VITE_NHOST_REGION, VITE_NHOST_BACKEND_URL.
Ensure cors.json allows requests from your Nhost frontend domain(s).
Phase 2: Prompt System Enhancement
Integrate credit checking into the existing generation flow managed by StorageSystem.
Credit Check Logic:
Modify StorageSystem.submitWorkflowToComfy or create a wrapper function (e.g., initiateGeneration).
Before calling submitWorkflowToComfy:
Fetch the current user's credit balance using a GraphQL query (users table).
Define a credit cost for the specific toolId. (This could be stored in the comfyui_servers table or a new tools table).
Compare the user's balance with the cost. If insufficient, throw an error (throw new Error('Insufficient credits.')) and display it to the user via the toast system.
Leverage Existing Flow: The rest of the flow (uploading input, getting workflow, submitting, polling, saving to R2 via worker) remains largely the same within StorageSystem.
Phase 3: Pricing & Credit System Implementation
Build the comprehensive credit system with plans, bundles, deductions, and free grants.
Database Schema (Nhost/Hasura):
users Table: Ensure it has credits (integer, default 0), plan_type (text/enum: 'free', 'monthly_basic', 'monthly_pro', 'bundle'), last_free_credit_grant (timestamp nullable), subscription_provider (text, e.g., 'stripe'), subscription_id (text nullable).
plans Table: (Optional but recommended) id, name (e.g., 'Monthly Basic'), price_monthly (numeric), credits_included (integer, maybe null for unlimited), stripe_price_id (text).
credit_bundles Table: id, name (e.g., '100 Credits'), price (numeric), credits_awarded (integer), stripe_price_id (text).
credit_transactions Table: id (uuid), user_id (uuid, FK to users), change_amount (integer, positive for additions, negative for deductions), reason (text: 'purchase_bundle', 'purchase_plan', 'usage_reimagine', 'usage_img2video', 'free_daily_grant', 'admin_grant', 'refund'), timestamp (timestamptz default now()), related_job_id (text nullable, links to ComfyUI prompt ID or project ID).
Backend Logic (Nhost Functions / Hasura Actions):
Payment Webhooks (Stripe/Paddle):
Create Nhost Functions (e.g., /api/webhooks/stripe).
Configure Stripe/Paddle to send events (e.g., checkout.session.completed, invoice.paid) to this function URL.
The function verifies the webhook signature, extracts payment details, and uses a Hasura GraphQL mutation (with admin privileges) to:
Update the users table: Set plan_type, add credits (for bundles or initial plan grants), store subscription_id.
Log the transaction in credit_transactions.
Daily Free Credits (Scheduled Function):
Create a Nhost Function (e.g., /api/cron/grant-free-credits).
Configure it as a cron job in nhost/config.yaml to run daily.
The function queries users where last_free_credit_grant is null or older than 24 hours.
For each eligible user, it performs a Hasura mutation to add the free credit amount (make this amount configurable, e.g., 100) and update last_free_credit_grant to the current time. Log transactions in credit_transactions.
Credit Deduction (Server-Side - Recommended):
Option A (Worker Trigger): Modify the Cloudflare worker (aigenius-api-worker). After successfully saving to R2, have the worker make an authenticated call to a new Nhost Function (e.g., /api/actions/deduct-credits). Pass the userId, toolId, promptId, and potentially generation duration (if tracked).
Option B (Hasura Event Trigger): Create a Hasura Event Trigger on the projects table (on insert). This trigger calls a Nhost Function.
Deduction Function: This Nhost Function calculates the credit cost (based on toolId, duration, etc.), performs a Hasura mutation to subtract credits from the users table (checking for sufficient balance again as a safeguard), and logs the deduction in credit_transactions.
Frontend Implementation:
Display:
Fetch user data (including credits, plan_type) using useUserData or GraphQL queries and display it in the Navbar/Dashboard.
Fetch available plans and bundles via GraphQL in src/pages/MyPlan.jsx.
Purchase Flow:
In MyPlan.jsx, add buttons for each plan/bundle.
On click, call a Nhost Function that creates a Stripe Checkout session (or initiates Paddle checkout) and redirects the user to the payment page.
Stopwatch & Client-Side Deduction (Alternative to Server-Side):
Add state (startTime, endTime, duration) to tool pages (ReImagine.jsx, Img2Video.jsx).
Start timer in handleSubmit. Stop timer when pollComfyOutputAndSave resolves.
Calculate cost: cost = durationInSeconds * creditsPerSecond.
In handleSaveProject (or just after successful generation), call a Hasura mutation from the frontend to deduct credits and log the transaction. This is less reliable than server-side deduction.
Add a UI element (e.g., <Typography>) next to the "Generate" button to display the running timer.
State Management: Use React Context or Zustand to manage and update the user's credit balance globally after purchases or deductions.
Next Steps:
Review & Refine: Go over this plan. Are there any immediate questions or adjustments needed?
Prioritize: Decide whether to tackle the migration first, or integrate one of the enhancements alongside it. Migrating first is generally recommended to establish the new baseline.
Implementation: Start implementing Phase 1, setting up Nhost locally and tackling the Authentication migration.


Refined Nhost/Hasura Database Schema for AiGenius
This schema builds upon the previous setup guide, incorporating the wallet system, passes/tiers, and preparing for future platform integration. We'll define the tables, columns, types, and basic relationships. You'll implement this using the Hasura Console (https://local.hasura.local.nhost.run).
(Remember to use the Nhost CLI to create migrations after defining these in the UI: nhost metadata pull, nhost migrations create <name>, nhost up)
1. users Table (public schema)
(View on auth.users, Nhost manages the underlying auth table)
Purpose: Stores core user information and links to other parts of the system.
Columns to Add/Ensure:
id (uuid, PK) - Provided by Nhost Auth
email (text) - Provided by Nhost Auth
displayName (text, nullable) - Provided by Nhost Auth (use for username)
avatarUrl (text, nullable) - Provided by Nhost Auth
created_at (timestamptz) - Provided by Nhost Auth
credits (integer, not null, Default: 100) - The central wallet balance for the user across all platforms.
active_subscription_id (uuid, nullable, FK -> subscriptions.id) - Points to the user's currently active subscription record, if any. Null if they are on a free/bundle-only plan.
last_free_credit_grant_at (timestamptz, nullable) - Tracks the last time daily free credits were granted. Used by the daily cron job.
Permissions (user role):
Select: Allow if id = X-Nhost-User-Id. Select id, email, displayName, avatarUrl, created_at, credits, active_subscription_id.
Update: Allow if id = X-Nhost-User-Id. Allow updates only for displayName, avatarUrl. Do NOT allow users to update credits or active_subscription_id directly.
2. plans Table (public schema)
Purpose: Defines the available passes, tiers, and subscription plans.
Columns:
id (text, PK) - Unique identifier (e.g., visitor_free, visitor_paid, citizen_lite, citizen_elite). Using text makes it readable.
name (text, not null) - User-friendly name (e.g., "Visitor Pass: Free Tier", "Citizen Pass: Lite Tier").
description (text, nullable) - Details about the plan.
price_monthly (numeric, nullable) - Monthly cost (e.g., 10.00 for Citizen Lite). Null for free/bundle plans. Store in a consistent currency (e.g., USD).
is_bundle_based (boolean, not null, Default: false) - true for "Visitor Pass: Paid Tier" where usage depends solely on purchased bundles.
is_subscription (boolean, not null, Default: false) - true for recurring plans like "Citizen Pass: Lite/Elite".
features (jsonb, nullable) - Store plan-specific features (e.g., {"fast_generation_limit_hours": 300, "slow_generation": "unlimited", "byo_hardware": false}). This provides flexibility.
provider_price_id (text, nullable) - The corresponding Price ID from your payment provider (Stripe/Paddle) for subscriptions.
is_active (boolean, not null, Default: true) - Allows you to enable/disable plans easily.
sort_order (integer, nullable, Default: 0) - To control display order in the UI.
Sample Data:
id='visitor_free', name='Visitor Pass: Free Tier', is_bundle_based=false, is_subscription=false, price_monthly=null
id='visitor_paid', name='Visitor Pass: Paid Tier', is_bundle_based=true, is_subscription=false, price_monthly=null
id='citizen_lite', name='Citizen Pass: Lite Tier', is_bundle_based=false, is_subscription=true, price_monthly=10.00, provider_price_id='stripe_price_id_lite', features='{"fast_generation_limit_hours": 300, ...}'
id='citizen_elite', name='Citizen Pass: Elite Tier', is_bundle_based=false, is_subscription=true, price_monthly=10.00, provider_price_id='stripe_price_id_elite', features='{"byo_hardware": true, ...}'
Permissions (user role):
Select: Allow access without checks. Select all relevant columns needed for display.
3. subscriptions Table (public schema)
Purpose: Tracks individual user subscriptions to plans.
Columns:
id (uuid, PK, Default: gen_random_uuid())
user_id (uuid, not null, FK -> users.id)
plan_id (text, not null, FK -> plans.id)
status (text, not null, Default: 'active') - e.g., 'active', 'canceled', 'past_due', 'trialing'.
current_period_start (timestamptz, not null) - Start date of the current billing cycle.
current_period_end (timestamptz, not null) - End date of the current billing cycle / when access expires.
cancel_at_period_end (boolean, not null, Default: false) - If the subscription is set to cancel at the end of the period.
provider (text, nullable) - e.g., 'stripe', 'paddle'.
provider_subscription_id (text, nullable, Unique) - The ID from the payment provider.
created_at (timestamptz, Default: now())
updated_at (timestamptz, Default: now()) - (Use DB triggers or Hasura Event Triggers to auto-update this)
Relationships:
Define the Foreign Keys for user_id and plan_id. Hasura will suggest relationship names (e.g., user, plan).
Go to the users table -> Relationships tab -> Add a Manual Relationship. Name it active_subscription, Type: Object Relationship, Reference Schema: public, Reference Table: subscriptions, From column: active_subscription_id, To column: id.
Permissions (user role):
Select: Allow if user_id = X-Nhost-User-Id. Select columns needed for display (status, plan details via relationship, period end).
Insert/Update/Delete: Disallow direct modification by users. These should only be managed via backend Nhost Functions triggered by payment webhooks or admin actions.
4. credit_bundles Table (public schema)
Purpose: Defines the one-time credit purchase options.
Columns:
id (text, PK) - Unique identifier (e.g., bundle_1usd_30, bundle_5usd_160).
name (text, not null) - User-friendly name (e.g., "$1 Credit Pack").
description (text, nullable)
price (numeric, not null) - Cost of the bundle.
credits_awarded (integer, not null) - Credits granted upon purchase.
provider_product_id (text, nullable) - Product ID from Stripe/Paddle.
provider_price_id (text, nullable) - Price ID from Stripe/Paddle.
is_active (boolean, not null, Default: true)
sort_order (integer, nullable, Default: 0)
Permissions (user role):
Select: Allow access without checks. Select all columns needed for display.
5. credit_transactions Table (public schema)
Purpose: Audit log for every change to a user's credit balance. Essential for tracking and debugging.
Columns:
id (uuid, PK, Default: gen_random_uuid())
user_id (uuid, not null, FK -> users.id)
change_amount (integer, not null) - Positive for additions (purchase, grant), negative for deductions (usage).
balance_after (integer, not null) - User's credits balance after this transaction. (Requires careful implementation in the function/action that creates the log).
reason (text, not null) - Predefined reason code (e.g., purchase_bundle, subscription_citizen_lite, usage_reimagine_fast, free_daily_grant, admin_adjustment).
related_id (text, nullable) - ID related to the reason (e.g., credit_bundles.id, subscriptions.id, projects.id).
notes (text, nullable) - Optional extra details.
created_at (timestamptz, Default: now())
Relationships: Define FK for user_id.
Permissions (user role):
Select: Allow if user_id = X-Nhost-User-Id. Select columns needed for display.
Insert/Update/Delete: Disallow direct access. Transactions must be created by backend functions/actions.
6. projects Table (public schema - Modifications)
Purpose: Stores metadata about generated outputs.
Columns to Add/Modify:
id (uuid, PK, Default: gen_random_uuid())
user_id (uuid, not null, FK -> users.id)
prompt_id (text, nullable) - ComfyUI prompt ID
output_url (text, not null)
output_r2_key (text, not null)
tool_id (text, nullable)
original_file_name (text, nullable)
prompt_used (text, nullable)
content_type (text, nullable)
comfy_output_file_name (text, nullable)
output_type (text, nullable) - 'image' or 'video'
created_at (timestamptz, Default: now())
saved_at (timestamptz, Default: now())
credit_cost (integer, nullable) - Credits deducted for this specific generation.
generation_type (text, nullable) - e.g., 'fast', 'slow'. To determine cost.
generation_duration_ms (integer, nullable) - Duration if costing by time.
Permissions (user role):
Insert: Allow. Preset user_id = X-Nhost-User-Id. Allow insert for all relevant columns except credit_cost (this should be calculated and set by the backend deduction logic).
Select: Allow if user_id = X-Nhost-User-Id. Select needed columns.
Delete: Allow if user_id = X-Nhost-User-Id.
7. prompts Table (public schema - Modifications)
Purpose: Logs initial prompt attempts.
Columns to Add/Modify:
id (uuid, PK, Default: gen_random_uuid())
user_id (uuid, not null, FK -> users.id)
tool_id (text, nullable)
original_file_name (text, nullable)
prompt_text (text, nullable)
workflow_identifier (text, nullable)
status (text, nullable, Default: 'submitted') - Could be updated later to 'processing', 'completed', 'failed'.
created_at (timestamptz, Default: now())
project_id (uuid, nullable, FK -> projects.id) - Optional: Link this log to the final project entry once generation succeeds.
Permissions (user role):
Insert: Allow. Preset user_id = X-Nhost-User-Id. Allow insert for relevant columns.
Select: Allow if user_id = X-Nhost-User-Id. Select needed columns.
Update: Maybe allow users to update the status? Or handle this via backend logic. Restrict other updates.
8. comfyui_servers Table (public schema - Modifications)
Purpose: Stores ComfyUI instance details and potentially costs.
Columns to Add/Modify:
tool_id (text, PK)
url (text, not null)
description (text, nullable)
created_at (timestamptz, Default: now())
cost_per_second_fast (numeric, nullable, Default: 0.1) - Example cost.
cost_per_second_slow (numeric, nullable, Default: 0.01)
fixed_cost_fast (integer, nullable) - Alternative fixed cost per job.
fixed_cost_slow (integer, nullable)
is_active (boolean, not null, Default: true)
Permissions (user role):
Select: Allow access without checks. Select tool_id, url, description, and potentially the cost columns if needed client-side (though calculation is safer on the backend).
Backend Logic is Key:
Remember, defining the tables is just the first step. The core logic for:
Handling payments and updating subscriptions (subscriptions, users.active_subscription_id).
Granting daily free credits (users.credits, users.last_free_credit_grant_at).
Calculating usage costs (projects.credit_cost, projects.generation_type).
Securely deducting credits (users.credits).
Logging all transactions (credit_transactions).
...must be implemented in secure Nhost Functions or Hasura Actions/Remote Schemas, triggered by webhooks, cron jobs, or specific events. Avoid doing sensitive calculations or direct credit modifications from the frontend application code.
This refined schema provides a solid foundation for your "Create" platform and considers the broader AiGenius vision.




Nhost/Hasura Basic Database Setup Guide
This guide will walk you through creating the necessary tables and setting basic permissions for your application using the Hasura Console.
Accessing the Hasura Console:
Make sure your Nhost local environment is running (nhost dev).
Open your browser and navigate to the Hasura URL provided: https://local.hasura.local.nhost.run (or your cloud equivalent later).
1. Modifying the users Table:
Nhost automatically creates an auth.users table (and a public users view linked to it). We need to add custom columns like credits to the public.users view/table (or preferably, manage user profile data in a separate profiles table linked one-to-one with auth.users - let's add to public.users for simplicity now).
Go to the Data tab in the Hasura Console.
In the left sidebar, under the public schema, find the users table/view. Click on it.
Go to the Modify tab for the users table.
Add Columns:
Click "Add Column".
Column Name: credits
Type: Integer
Nullable: No (uncheck the box)
Default Value: SQL Expression -> enter 100 (or your desired starting credits)
Click "Add Column".
(Optional) Column Name: plan_type
(Optional) Type: Text
(Optional) Nullable: Yes (check the box)
(Optional) Default Value: Custom -> enter 'free'
(Optional) Click "Add Column".
(Optional) Column Name: last_free_credit_grant
(Optional) Type: Timestamp with time zone (timestamptz)
(Optional) Nullable: Yes
(Optional) Click "Add Column".
Permissions:
Go to the Permissions tab for the users table.
Enter role: user (if not already present, type it and hit Enter).
Select:
Configure permissions.
Row select permissions: Choose "With custom check".
Condition: { "id": { "_eq": "X-Nhost-User-Id" } } (This allows users to select only their own user record).
Column select permissions: Check the columns the user should be able to read (e.g., id, email, displayName, credits, avatarUrl, created_at, plan_type, etc.).
Click "Save Permissions".
Update:
Configure permissions.
Row update permissions: Choose "With custom check".
Condition: { "id": { "_eq": "X-Nhost-User-Id" } } (Users can update their own record).
Column update permissions: Check only the columns users should be allowed to modify directly (e.g., displayName, avatarUrl). Do NOT allow users to update their own credits directly here. Credit updates should happen via secure backend logic (Nhost Functions/Actions).
Click "Save Permissions".
2. Creating the comfyui_servers Table:
This table stores the URLs for your ComfyUI instances.
Go to the Data tab.
Click "Create Table".
Table Name: comfyui_servers
Schema: public (usually default)
Columns:
tool_id (Type: Text, Primary Key: Check the box) - e.g., 'reimagine', 'img2video'
url (Type: Text, Nullable: No) - The base URL of the ComfyUI instance.
(Optional) description (Type: Text, Nullable: Yes)
(Optional) created_at (Type: Timestamp with time zone, Nullable: No, Default: now())
Set tool_id as the Primary Key.
Click "Create Table".
Add Data: Go to the "Browse Rows" tab for the new table and insert rows for your tools (e.g., tool_id: 'reimagine', url: 'http://your-comfy-url:8188').
Permissions:
Go to the Permissions tab.
Enter role: user.
Select: Configure permissions. Allow users to select all rows (Without any checks) and check all columns (tool_id, url, description). Save.
(Restrict Insert/Update/Delete for user role unless needed).
3. Creating the prompts Table:
This table logs prompt attempts.
Go to the Data tab.
Click "Create Table".
Table Name: prompts
Schema: public
Columns:
id (Type: UUID, Primary Key: Check the box, Default: gen_random_uuid())
user_id (Type: UUID, Nullable: No) - This will link to the user.
tool_id (Type: Text, Nullable: Yes)
original_file_name (Type: Text, Nullable: Yes)
prompt_text (Type: Text, Nullable: Yes)
workflow_identifier (Type: Text, Nullable: Yes) - e.g., the Nhost Storage File ID
status (Type: Text, Nullable: Yes, Default: 'submitted')
created_at (Type: Timestamp with time zone, Nullable: No, Default: now())
Set id as the Primary Key.
Click "Create Table".
Relationships (Foreign Key):
Go to the Relationships tab for the prompts table.
Click "Add" under Foreign Keys.
Reference Table: public.users
From Column(s): user_id
To Column(s): id
Click "Save". Hasura might suggest relationship names (e.g., user).
Permissions:
Go to the Permissions tab.
Enter role: user.
Insert: Configure permissions. Allow (Without any checks). Check columns the user can insert (tool_id, original_file_name, prompt_text, workflow_identifier, status). Important: Under Column insert presets, set user_id to X-Nhost-User-Id. Save.
Select: Configure permissions. Set row permissions to With custom check: { "user_id": { "_eq": "X-Nhost-User-Id" } }. Check all columns needed for display. Save.
(Restrict Update/Delete for user role unless needed).
4. Creating the projects Table:
This table stores the final generated project metadata.
Go to the Data tab.
Click "Create Table".
Table Name: projects
Schema: public
Columns:
id (Type: UUID, Primary Key: Check the box, Default: gen_random_uuid())
user_id (Type: UUID, Nullable: No)
prompt_id (Type: Text, Nullable: Yes) - The ComfyUI prompt ID
output_url (Type: Text, Nullable: No) - The R2 public URL
output_r2_key (Type: Text, Nullable: No) - The key in your R2 bucket
tool_id (Type: Text, Nullable: Yes)
original_file_name (Type: Text, Nullable: Yes)
prompt_used (Type: Text, Nullable: Yes)
content_type (Type: Text, Nullable: Yes) - e.g., 'image/png', 'video/mp4'
comfy_output_file_name (Type: Text, Nullable: Yes)
output_type (Type: Text, Nullable: Yes) - 'image' or 'video'
created_at (Type: Timestamp with time zone, Nullable: No, Default: now())
saved_at (Type: Timestamp with time zone, Nullable: No, Default: now())
Set id as the Primary Key.
Click "Create Table".
Relationships (Foreign Key):
Go to the Relationships tab.
Add a Foreign Key constraint from projects.user_id to public.users.id (similar to the prompts table).
Permissions:
Go to the Permissions tab.
Enter role: user.
Insert: Configure permissions. Allow (Without any checks). Check all relevant columns. Set column preset user_id to X-Nhost-User-Id. Save.
Select: Configure permissions. Set row permissions to With custom check: { "user_id": { "_eq": "X-Nhost-User-Id" } }. Check all columns needed for display. Save.
Delete: Configure permissions. Set row permissions to With custom check: { "user_id": { "_eq": "X-Nhost-User-Id" } }. Save.
(Restrict Update for user role unless needed).
Important Notes:
Track Schema: After making changes in the Hasura Console UI, remember to export the metadata and create migrations using the Nhost CLI to keep your schema changes version-controlled:
nhost metadata pull
nhost migrations create <migration-name> (e.g., nhost migrations create setup-initial-tables)
Apply migrations: nhost up
Permissions: The permissions set here are basic. You might need more granular control later, especially for admin roles or different plan types. X-Nhost-User-Id is a session variable automatically provided by Nhost/Hasura for the logged-in user.
Data Types: Choose appropriate data types. text is flexible, but use integer, numeric, boolean, timestamptz where applicable.
Defaults: Using now() for timestamps and gen_random_uuid() for IDs is common and useful.
This setup provides the foundational database structure needed for the migrated features. You can now test the GraphQL queries and mutations defined in the previous code step against these tables.

Phase 1 Migration Milestone Report: Firebase to Nhost Complete
Objective: Replace core Firebase services (Auth, Firestore, Storage for workflows) with Nhost equivalents in the "Create.aigenius_space" React application.
Status: Completed
Summary of Completed Steps:
Environment Setup:
Nhost CLI installed and project initialized.
Local Nhost development environment (Docker containers for Postgres, Hasura, Auth, Storage) is operational.
Required Nhost dependencies (@nhost/react, @nhost/react-auth, @nhost/react-apollo, etc.) installed.
Firebase dependencies removed.
.env file configured with Nhost backend URL for local development.
Authentication Migration:
Nhost client initialized (src/services/nhost.js).
Application wrapped with <NhostProvider> (src/main.jsx).
Authentication service (src/services/auth.js) rewritten to use Nhost Auth functions (signIn, signUp, signOut).
Login, Register, and Navbar components updated to use Nhost hooks (useAuthenticationStatus, useSignOut, etc.) and auth functions.
Protected routing logic updated in src/App.jsx.
Database Migration:
Basic database schema (users, projects, prompts, comfyui_servers) defined in Hasura, including custom columns (e.g., users.credits) and relationships.
Basic row-level permissions configured in Hasura for the user role.
Data fetching and manipulation logic updated to use GraphQL (via @apollo/client hooks useQuery/useMutation or nhost.graphql.request) in:
src/services/storageSystem.js (for Comfy URLs, logging prompts, saving projects)
src/services/auth.js (for getUserData)
src/components/Projects.jsx
src/pages/History.jsx
src/pages/ProfileSettings.jsx
Storage Migration (Workflows):
Workflow template fetching logic in src/services/storageSystem.js updated to use Nhost Storage (nhost.storage.getPresignedUrl).
(Assumption: Workflow JSON files have been uploaded to Nhost Storage).
API Worker Update:
Cloudflare Worker (aigenius-api-worker) updated to verify Nhost JWTs using the jose library.
Worker configuration (wrangler.toml) updated with necessary secrets (NHOST_JWT_SECRET, R2 credentials) and environment variables (R2_BUCKET_NAME, R2_ACCOUNT_ID, etc.).
Worker successfully interacts with the specified R2 bucket (user-content).
Firebase Cleanup:
Firebase configuration files (firebase.json, .rules, etc.) deleted.
Firebase initialization code (src/services/firebase.js) removed.
Remaining Firebase code references, dependencies, and environment variables removed/verified.
Outcome:
The application's core backend functionality (Authentication, Database, Workflow Storage) is now powered by Nhost running locally. The frontend interacts with Nhost via its SDK and GraphQL. The Cloudflare Worker continues to handle ComfyUI interaction and R2 uploads but is now secured using Nhost authentication.
Next Steps:
Thorough Testing: Extensively test all migrated features locally: login, registration, profile updates, project generation (including polling and saving), history display, project display/deletion. Monitor browser console and Nhost logs (nhost logs auth, nhost logs hasura, etc.) for errors.
Phase 2/3 Implementation: Proceed with implementing the enhancements:
Prompt System: Integrate credit checking before generation.
Pricing/Credit System: Implement the backend logic (Nhost Functions for payments, cron jobs, secure deductions) and frontend UI for plans, bundles, and purchases.
Deployment: Plan and execute deployment of the Nhost backend (e.g., Nhost Cloud) and the updated frontend/worker to production environments.
Phase 1 was a significant step in modernizing your backend. Congratulations!

