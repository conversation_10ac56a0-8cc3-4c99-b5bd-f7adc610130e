// src/components/design-system/Typography/Caption.jsx
import React from 'react';
import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledCaption = styled(Typography)(({ theme, variant, color }) => ({
  fontSize: theme.typography.fontSize?.xs || '0.75rem',
  lineHeight: theme.typography.lineHeight?.normal || 1.5,
  fontWeight: theme.typography.fontWeight?.normal || 400,
  
  // Variant styles
  ...(variant === 'muted' && {
    color: theme.palette.text.secondary,
  }),
  ...(variant === 'subtle' && {
    color: theme.palette.text.disabled,
  }),
  ...(variant === 'error' && {
    color: theme.palette.error.main,
  }),
  ...(variant === 'success' && {
    color: theme.palette.success.main,
  }),
  ...(variant === 'warning' && {
    color: theme.palette.warning.main,
  }),
  ...(color && {
    color: color,
  }),
}));

/**
 * Caption component for small descriptive text
 * 
 * @param {Object} props
 * @param {'muted'|'subtle'|'error'|'success'|'warning'} props.variant - Caption variant
 * @param {string} props.color - Custom text color
 * @param {'span'|'p'|'div'} props.as - HTML element to render
 * @param {React.ReactNode} props.children - Caption text
 */
const Caption = ({ 
  variant = 'muted',
  color,
  as = 'span',
  children,
  ...props 
}) => {
  return (
    <StyledCaption
      component={as}
      variant={variant}
      color={color}
      {...props}
    >
      {children}
    </StyledCaption>
  );
};

export default Caption;
