// src/hooks/useCreditWallet.js
// Wallet state management hook for the unified credit system

import { useState, useEffect, useCallback } from 'react';
import { useUserId } from '@nhost/react';
import { CreditService } from '../services/creditService';
import { canClaimFreeCredits, formatCreditBalance } from '../utils/creditCalculations';

/**
 * Hook for managing credit wallet state
 * @returns {Object} Wallet state and operations
 */
export const useCreditWallet = () => {
  const userId = useUserId();
  const [wallet, setWallet] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [canClaimFree, setCanClaimFree] = useState(false);
  const [usagePercentage, setUsagePercentage] = useState(0);

  // Fetch wallet information
  const refreshWallet = useCallback(async () => {
    if (!userId) {
      setWallet(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const walletInfo = await CreditService.getUserWalletInfo(userId);
      setWallet(walletInfo);

      // Check if user can claim free credits
      if (walletInfo.activePass?.pass_type?.tier === 'Free') {
        const eligibility = canClaimFreeCredits(
          walletInfo,
          walletInfo.activePass.pass_type.tier,
          walletInfo.activePass.pass_type.category
        );
        setCanClaimFree(eligibility.eligible);
      } else {
        setCanClaimFree(false);
      }

      // Calculate usage percentage for free tier
      if (walletInfo.activePass?.pass_type?.tier === 'Free') {
        const dailyCredits = 3600; // 1 hour for free tier
        const percentage = Math.min(100, Math.max(0, (walletInfo.credits / dailyCredits) * 100));
        setUsagePercentage(percentage);
      } else {
        setUsagePercentage(0);
      }

    } catch (err) {
      console.error('[useCreditWallet] Error fetching wallet:', err);
      setError(err.message || 'Failed to fetch wallet information');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Claim daily free credits
  const claimFreeCredits = useCallback(async () => {
    if (!userId || !canClaimFree) {
      throw new Error('Cannot claim free credits');
    }

    try {
      setLoading(true);
      const result = await CreditService.claimDailyCredits(userId);
      
      // Refresh wallet after claiming
      await refreshWallet();
      
      return result;
    } catch (err) {
      console.error('[useCreditWallet] Error claiming free credits:', err);
      setError(err.message || 'Failed to claim free credits');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [userId, canClaimFree, refreshWallet]);

  // Subscribe to wallet updates (could be enhanced with real-time subscriptions)
  const subscribeToWalletUpdates = useCallback(() => {
    // For now, just refresh periodically
    const interval = setInterval(refreshWallet, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [refreshWallet]);

  // Initial load and setup
  useEffect(() => {
    refreshWallet();
  }, [refreshWallet]);

  // Format credit balance for display
  const formattedBalance = wallet ? formatCreditBalance(wallet.credits) : null;

  return {
    // Wallet state
    wallet,
    loading,
    error,
    
    // Formatted data
    formattedBalance,
    usagePercentage,
    
    // Eligibility flags
    canClaimFree,
    
    // Operations
    refreshWallet,
    claimFreeCredits,
    subscribeToWalletUpdates,
    
    // Computed values
    hasCredits: wallet ? wallet.credits > 0 : false,
    isFreeTier: wallet?.activePass?.pass_type?.tier === 'Free',
    isUnlimited: wallet?.hasUnlimitedUsage || false,
    allowedServerTypes: wallet?.allowedServerTypes || ['slow']
  };
};

export default useCreditWallet;
