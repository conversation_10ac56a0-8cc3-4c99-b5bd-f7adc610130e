// src/utils/testGenerationService.js
// Test utilities for GenerationService validation

import { GenerationService } from '../services/generationService';
import { nhost } from '../services/nhost';

/**
 * Test the upload workflow for user_inputs bucket
 * This function validates that files can be uploaded and appear in storage.files table
 */
export async function testUserInputsUpload() {
    console.log('🧪 Testing user_inputs bucket upload workflow...');
    
    try {
        // Check if user is authenticated
        const user = nhost.auth.getUser();
        const session = nhost.auth.getSession();
        
        if (!user || !session) {
            throw new Error('User must be authenticated to test upload workflow');
        }
        
        console.log(`✅ User authenticated: ${user.id}`);
        
        // Create a test image file
        const canvas = document.createElement('canvas');
        canvas.width = 100;
        canvas.height = 100;
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(0, 0, 100, 100);
        
        // Convert canvas to blob
        const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
        const testFile = new File([blob], `test_upload_${Date.now()}.png`, { type: 'image/png' });
        
        console.log(`📁 Created test file: ${testFile.name} (${testFile.size} bytes)`);
        
        // Test upload to user_inputs bucket
        const uploadResult = await nhost.storage.upload({
            bucketId: GenerationService.bucketIds.USER_INPUTS,
            file: testFile,
            name: `test/${user.id}/${testFile.name}`
        });
        
        if (uploadResult.error) {
            throw new Error(`Upload failed: ${uploadResult.error.message}`);
        }
        
        const fileId = uploadResult.fileMetadata.id;
        console.log(`✅ File uploaded successfully. File ID: ${fileId}`);
        
        // Verify file appears in storage.files table
        const fileQuery = `
            query GetUploadedFile($fileId: uuid!) {
                storage_files_by_pk(id: $fileId) {
                    id
                    name
                    bucket_id
                    size
                    mime_type
                    created_at
                }
            }
        `;
        
        const { data, error } = await nhost.graphql.request(fileQuery, { fileId });
        
        if (error) {
            console.warn('⚠️ Could not verify file in storage.files table:', error);
        } else if (data?.storage_files_by_pk) {
            const fileRecord = data.storage_files_by_pk;
            console.log('✅ File verified in storage.files table:', {
                id: fileRecord.id,
                name: fileRecord.name,
                bucket_id: fileRecord.bucket_id,
                size: fileRecord.size,
                mime_type: fileRecord.mime_type,
                created_at: fileRecord.created_at
            });
            
            // Verify bucket_id matches expected
            if (fileRecord.bucket_id === GenerationService.bucketIds.USER_INPUTS) {
                console.log('✅ Bucket ID matches expected value');
            } else {
                console.error(`❌ Bucket ID mismatch. Expected: ${GenerationService.bucketIds.USER_INPUTS}, Got: ${fileRecord.bucket_id}`);
            }
        } else {
            console.error('❌ File not found in storage.files table');
        }
        
        // Test presigned URL generation
        const { presignedUrl, error: urlError } = await nhost.storage.getPresignedUrl({ fileId });
        
        if (urlError) {
            console.error('❌ Failed to get presigned URL:', urlError);
        } else {
            console.log('✅ Presigned URL generated successfully');
        }
        
        // Clean up test file
        const { error: deleteError } = await nhost.storage.delete({ fileId });
        
        if (deleteError) {
            console.warn('⚠️ Failed to clean up test file:', deleteError);
        } else {
            console.log('✅ Test file cleaned up successfully');
        }
        
        return {
            success: true,
            fileId,
            bucketId: GenerationService.bucketIds.USER_INPUTS,
            fileName: testFile.name,
            fileSize: testFile.size
        };
        
    } catch (error) {
        console.error('❌ Upload workflow test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Test bucket configuration validation
 */
export async function testBucketValidation() {
    console.log('🧪 Testing bucket configuration validation...');

    try {
        const validation = await GenerationService.validateBucketConfiguration();

        console.log('📊 Bucket validation results:', validation);

        // Check each bucket
        Object.entries(validation).forEach(([bucketName, config]) => {
            if (config.valid) {
                console.log(`✅ ${bucketName} bucket (${config.id}): Valid`);
            } else {
                console.error(`❌ ${bucketName} bucket (${config.id}): Invalid - ${config.error}`);
            }
        });

        return validation;

    } catch (error) {
        console.error('❌ Bucket validation test failed:', error);
        return { error: error.message };
    }
}

/**
 * Test health check functionality
 */
export async function testHealthCheck() {
    console.log('🧪 Testing health check functionality...');

    try {
        // Check if user is authenticated
        const user = nhost.auth.getUser();

        if (!user) {
            throw new Error('User must be authenticated to test health check');
        }

        console.log(`✅ User authenticated: ${user.id}`);

        // Test with a sample tool ID and server type
        const testToolId = 'img2video'; // Replace with actual tool ID
        const testServerType = 'slow';

        console.log(`🏥 Running health check for tool: ${testToolId}, server type: ${testServerType}`);

        const healthResults = await GenerationService.performHealthCheck(user.id, testToolId, testServerType);

        console.log('📊 Health check results:', healthResults);

        // Analyze results
        if (healthResults.overall) {
            console.log('✅ Overall health check: PASSED');
        } else {
            console.error('❌ Overall health check: FAILED');
        }

        // Database access details
        if (healthResults.databaseAccess.passed) {
            console.log('✅ Database access: PASSED');
            Object.entries(healthResults.databaseAccess.details).forEach(([table, result]) => {
                if (result.success) {
                    console.log(`  ✅ ${table} table: Accessible`);
                } else {
                    console.error(`  ❌ ${table} table: ${result.error}`);
                }
            });
        } else {
            console.error('❌ Database access: FAILED');
        }

        // Active servers details
        if (healthResults.activeServers.passed) {
            console.log(`✅ Active servers: PASSED (${healthResults.activeServers.details.serversFound} found)`);
            healthResults.activeServers.details.servers.forEach(server => {
                console.log(`  🖥️ Server: ${server.id} - ${server.url} (${server.server_type})`);
            });
        } else {
            console.error('❌ Active servers: FAILED');
        }

        return healthResults;

    } catch (error) {
        console.error('❌ Health check test failed:', error);
        return { error: error.message };
    }
}

// Add a test for credit calculation
export async function testCreditCalculation() {
    console.log('🧪 Testing credit calculation...');
    
    try {
        // Check if user is authenticated
        const user = nhost.auth.getUser();
        
        if (!user) {
            throw new Error('User must be authenticated to test credit calculation');
        }
        
        console.log(`✅ User authenticated: ${user.id}`);
        
        // Test with different durations and server types
        const testCases = [
            { toolId: 'img2video', duration: 10000, serverType: 'slow' },
            { toolId: 'img2video', duration: 10000, serverType: 'fast' },
            { toolId: 'reimagine', duration: 30000, serverType: 'slow' },
            { toolId: 'reimagine', duration: 30000, serverType: 'fast' }
        ];
        
        const results = [];
        
        for (const test of testCases) {
            console.log(`🔢 Testing ${test.toolId} with ${test.duration}ms on ${test.serverType} server`);
            
            const cost = await GenerationService.calculateCreditCost(
                test.duration,
                test.serverType,
                test.toolId
            );
            
            results.push({
                ...test,
                calculatedCost: cost
            });
            
            console.log(`✅ Calculated cost: ${cost} credits`);
        }
        
        console.log('📊 Credit calculation test results:', results);
        
        return {
            success: true,
            results
        };
    } catch (error) {
        console.error('❌ Credit calculation test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Comprehensive test suite
 */
export async function runGenerationServiceTests() {
    console.log('🚀 Running GenerationService test suite...');

    const results = {
        healthCheck: null,
        bucketValidation: null,
        uploadWorkflow: null,
        creditCalculation: null,
        timestamp: new Date().toISOString()
    };

    // Test health check
    results.healthCheck = await testHealthCheck();

    // Test bucket validation
    results.bucketValidation = await testBucketValidation();

    // Test upload workflow
    results.uploadWorkflow = await testUserInputsUpload();
    
    // Test credit calculation
    results.creditCalculation = await testCreditCalculation();

    console.log('📋 Test suite completed:', results);

    return results;
}

// Make functions available globally in development
if (import.meta.env.DEV) {
    window.testGenerationService = {
        testUserInputsUpload,
        testBucketValidation,
        testHealthCheck,
        testCreditCalculation,
        runGenerationServiceTests
    };

    console.log('🛠️ GenerationService test functions available globally:');
    console.log('- window.testGenerationService.testUserInputsUpload()');
    console.log('- window.testGenerationService.testBucketValidation()');
    console.log('- window.testGenerationService.testHealthCheck()');
    console.log('- window.testGenerationService.testCreditCalculation()');
    console.log('- window.testGenerationService.runGenerationServiceTests()');
}

