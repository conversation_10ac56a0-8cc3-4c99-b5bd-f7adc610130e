<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748771141065" clover="3.2.0">
  <project timestamp="1748771141065" name="All files">
    <metrics statements="163" coveredstatements="68" conditionals="95" coveredconditionals="28" methods="12" coveredmethods="7" elements="270" coveredelements="103" complexity="0" loc="163" ncloc="163" packages="2" files="5" classes="5"/>
    <package name="_utils">
      <metrics statements="2" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="nhost.ts" path="D:\AiGeniusSpace\aigenius-app-nhost\functions\_utils\nhost.ts">
        <metrics statements="2" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="credit">
      <metrics statements="161" coveredstatements="68" conditionals="91" coveredconditionals="28" methods="12" coveredmethods="7"/>
      <file name="handleCreditTransaction.ts" path="D:\AiGeniusSpace\aigenius-app-nhost\functions\credit\handleCreditTransaction.ts">
        <metrics statements="75" coveredstatements="0" conditionals="46" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="151" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="159" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="172" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="203" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="213" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="226" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="243" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="252" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="291" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="292" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="327" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="347" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
      </file>
      <file name="handleDailyCredits.ts" path="D:\AiGeniusSpace\aigenius-app-nhost\functions\credit\handleDailyCredits.ts">
        <metrics statements="50" coveredstatements="39" conditionals="30" coveredconditionals="15" methods="5" coveredmethods="3"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="65" count="2" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="75" count="2" type="stmt"/>
        <line num="78" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="94" count="1" type="stmt"/>
        <line num="98" count="3" type="stmt"/>
        <line num="99" count="3" type="stmt"/>
        <line num="101" count="3" type="cond" truecount="0" falsecount="1"/>
        <line num="102" count="0" type="stmt"/>
        <line num="110" count="3" type="stmt"/>
        <line num="114" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="3" type="stmt"/>
        <line num="123" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="126" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="127" count="1" type="stmt"/>
        <line num="135" count="2" type="stmt"/>
        <line num="136" count="2" type="stmt"/>
        <line num="139" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="1" type="cond" truecount="3" falsecount="0"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="153" count="2" type="stmt"/>
        <line num="160" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="161" count="2" type="stmt"/>
        <line num="172" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="173" count="2" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="D:\AiGeniusSpace\aigenius-app-nhost\functions\credit\index.ts">
        <metrics statements="9" coveredstatements="8" conditionals="8" coveredconditionals="7" methods="1" coveredmethods="1"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="4" type="stmt"/>
        <line num="12" count="4" type="cond" truecount="3" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="15" count="3" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
      </file>
      <file name="types.ts" path="D:\AiGeniusSpace\aigenius-app-nhost\functions\credit\types.ts">
        <metrics statements="27" coveredstatements="21" conditionals="7" coveredconditionals="6" methods="4" coveredmethods="3"/>
        <line num="6" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="7" count="3" type="stmt"/>
        <line num="8" count="3" type="stmt"/>
        <line num="9" count="3" type="stmt"/>
        <line num="10" count="3" type="stmt"/>
        <line num="11" count="3" type="stmt"/>
        <line num="12" count="3" type="stmt"/>
        <line num="18" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="19" count="3" type="stmt"/>
        <line num="20" count="3" type="stmt"/>
        <line num="21" count="3" type="stmt"/>
        <line num="132" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="133" count="3" type="stmt"/>
        <line num="134" count="3" type="stmt"/>
        <line num="135" count="3" type="stmt"/>
        <line num="136" count="3" type="stmt"/>
        <line num="137" count="3" type="stmt"/>
        <line num="138" count="3" type="stmt"/>
        <line num="139" count="3" type="stmt"/>
        <line num="140" count="3" type="stmt"/>
        <line num="146" count="3" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
