// src/hooks/useStopwatch.js
import { useState, useRef, useCallback, useEffect } from 'react';

// Formatting function with milliseconds
const formatTime = (timeMs) => {
    if (typeof timeMs !== 'number' || timeMs < 0) {
        return '00:00.000'; // Default for invalid input
    }
    const milliseconds = String(timeMs % 1000).padStart(3, '0');
    const totalSeconds = Math.floor(timeMs / 1000);
    const seconds = String(totalSeconds % 60).padStart(2, '0');
    const minutes = String(Math.floor(totalSeconds / 60)).padStart(2, '0');
    // const hours = String(Math.floor(totalMinutes / 60)).padStart(2, '0'); // Uncomment if hours are needed
    // return `${hours}:${minutes}:${seconds}.${milliseconds}`;
    return `${minutes}:${seconds}.${milliseconds}`; // Format MM:SS.ms
};

/**
 * Enhanced stopwatch hook with credit deduction integration for unified credit system.
 * @param {object} options - Configuration options.
 * @param {function(number): void} [options.onTick] - Callback triggered periodically. Receives current elapsed time (ms).
 * @param {number} [options.tickInterval=1000] - Interval (in ms) for the onTick callback.
 * @param {string} [options.userId] - User ID for credit deduction.
 * @param {string} [options.serverType='slow'] - Server type for cost calculation.
 * @param {string} [options.generationId] - Generation ID for credit tracking.
 * @param {function(Object): void} [options.onCreditDeduction] - Callback when credits are deducted.
 * @param {function(Error): void} [options.onCreditError] - Callback when credit deduction fails.
 * @returns {{
 * time: number, // Current elapsed time in ms (updates while running)
 * finalTime: number | null, // Time in ms when stopwatch was last stopped
 * formattedTime: string, // Formatted time string (shows finalTime if stopped, else current time)
 * isRunning: boolean,
 * currentCost: number, // Current cost in credits
 * start: () => void,
 * stop: (boolean) => Promise<number>, // Stops and returns final elapsed time, optionally deducts credits
 * reset: () => void,
 * pause: () => void,
 * resume: () => void
 * }}
 */
export const useStopwatch = ({
  onTick,
  tickInterval = 1000,
  userId = null,
  serverType = 'slow',
  generationId = null,
  onCreditDeduction = null,
  onCreditError = null
} = {}) => {
    const [time, setTime] = useState(0); // Current time being updated by interval
    const [isRunning, setIsRunning] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [finalTime, setFinalTime] = useState(null); // Stores the time when explicitly stopped
    const [currentCost, setCurrentCost] = useState(0); // Current cost in credits
    const intervalRef = useRef(null);
    const startTimeRef = useRef(0);
    const pausedTimeRef = useRef(0); // Total paused time
    const lastTickTimeRef = useRef(0); // For onTick interval logic

    // Clears the interval
    const cleanupInterval = useCallback(() => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
    }, []);

    const start = useCallback(() => {
        if (isRunning) return;
        console.log('[Stopwatch] Starting...');
        setFinalTime(null); // Clear any previous final time when starting a new run
        setTime(0);         // Reset current display time to 0
        startTimeRef.current = Date.now(); // Set the absolute start time
        lastTickTimeRef.current = startTimeRef.current; // Initialize for onTick

        cleanupInterval(); // Ensure no previous interval is running

        intervalRef.current = setInterval(() => {
            const now = Date.now();
            const currentTime = now - startTimeRef.current;
            setTime(currentTime); // Update the displayed time

            if (onTick && now - lastTickTimeRef.current >= tickInterval) {
                try {
                    onTick(currentTime);
                } catch (e) { console.error("Stopwatch onTick callback error:", e); }
                lastTickTimeRef.current = now;
            }
        }, 50); // Update display frequently for smoothness

        setIsRunning(true);
    }, [isRunning, onTick, tickInterval, cleanupInterval]);

    const stop = useCallback(() => {
        // If not running, return the last known final time or current time (which should be the stopped time)
        if (!isRunning) return finalTime ?? time;

        console.log('[Stopwatch] Stopping...');
        cleanupInterval();
        setIsRunning(false);
        const stoppedAt = Date.now() - startTimeRef.current;
        setTime(stoppedAt);      // Set the display time to the exact stop time
        setFinalTime(stoppedAt); // Store this as the final time
        return stoppedAt;
    }, [isRunning, time, finalTime, cleanupInterval]); // Added finalTime

    const reset = useCallback(() => {
        console.log('[Stopwatch] Resetting...');
        cleanupInterval();
        setIsRunning(false);
        setTime(0);
        setFinalTime(null); // Clear final time on reset
        startTimeRef.current = 0;
        lastTickTimeRef.current = 0;
    }, [cleanupInterval]);

    // Cleanup interval on component unmount
    useEffect(() => {
        return cleanupInterval;
    }, [cleanupInterval]);

    // Calculate current cost in credits (1 credit = 1 second)
    const currentCost = Math.ceil((finalTime !== null && !isRunning ? finalTime : time) / 1000);

    // Enhanced stop function with credit deduction
    const stopWithCreditDeduction = useCallback(async (shouldDeductCredits = true) => {
        const elapsedTime = stop(); // Call original stop function

        if (shouldDeductCredits && userId && generationId && elapsedTime > 0) {
            try {
                const { CreditService } = await import('../services/creditService');
                const seconds = elapsedTime / 1000;

                const result = await CreditService.deductCreditsForGeneration(
                    userId,
                    seconds,
                    serverType,
                    generationId
                );

                if (onCreditDeduction) {
                    onCreditDeduction(result);
                }

                return { elapsedTime, creditResult: result };
            } catch (error) {
                console.error('[useStopwatch] Credit deduction failed:', error);
                if (onCreditError) {
                    onCreditError(error);
                }
                return { elapsedTime, creditError: error };
            }
        }

        return { elapsedTime };
    }, [stop, userId, generationId, serverType, onCreditDeduction, onCreditError]);

    return {
        time, // Current live time while running
        finalTime, // The time at which stop() was called
        // Display finalTime if stopwatch is stopped and finalTime is set, otherwise display live time
        formattedTime: formatTime(finalTime !== null && !isRunning ? finalTime : time),
        isRunning,
        currentCost, // Current cost in credits
        start,
        stop, // Original stop function
        stopWithCreditDeduction, // Enhanced stop with credit deduction
        reset,
    };
};
