// src/hooks/useServerAccess.js
// Server access state management hook for the unified credit system

import { useState, useEffect, useCallback } from 'react';
import { useUserId } from '@nhost/react';
import { ServerAccessService } from '../services/serverAccessService';
import { PassService } from '../services/passService';
import { canAccessServerType, getAvailableServerTypes, getServerCostInfo } from '../utils/passUtils';
import { useCreditWallet } from './useCreditWallet';

/**
 * Hook for managing server access state
 * @param {string} toolId - Optional tool ID for server filtering
 * @returns {Object} Server access state and operations
 */
export const useServerAccess = (toolId = null) => {
  const userId = useUserId();
  const { wallet } = useCreditWallet();
  const [allowedServers, setAllowedServers] = useState([]);
  const [availableServers, setAvailableServers] = useState([]);
  const [currentPass, setCurrentPass] = useState(null);
  const [serverRestrictions, setServerRestrictions] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch server access information
  const refreshServerAccess = useCallback(async () => {
    if (!userId) {
      setAllowedServers([]);
      setAvailableServers([]);
      setCurrentPass(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Get user's current pass
      const passInfo = await PassService.getUserActivePass(userId);
      setCurrentPass(passInfo.activePass);

      if (passInfo.hasActivePass) {
        const pass = passInfo.activePass;
        const tier = pass.pass_type.tier;
        const category = pass.pass_type.category;
        const creditsBalance = wallet?.credits || 0;

        // Get allowed server types based on pass
        const allowedTypes = getAvailableServerTypes(tier, category, creditsBalance);
        setAllowedServers(allowedTypes);

        // Get available servers from database
        const servers = await ServerAccessService.getAvailableServers(userId, toolId);
        
        // Filter servers based on user access
        const accessibleServers = servers.filter(server => {
          const access = canAccessServerType(tier, category, server.server_type, creditsBalance);
          return access.allowed;
        });
        
        setAvailableServers(accessibleServers);

        // Set server restrictions
        const restrictions = {};
        ['slow', 'fast'].forEach(serverType => {
          const access = canAccessServerType(tier, category, serverType, creditsBalance);
          if (!access.allowed) {
            restrictions[serverType] = {
              reason: access.reason,
              suggestedUpgrade: access.suggestedUpgrade,
              needsCredits: access.needsCredits
            };
          }
        });
        setServerRestrictions(restrictions);

      } else {
        // No active pass - default to slow only
        setAllowedServers(['slow']);
        setAvailableServers([]);
        setServerRestrictions({
          fast: {
            reason: 'No active pass',
            suggestedUpgrade: { category: 'Visitor', tier: 'Paid' }
          }
        });
      }

    } catch (err) {
      console.error('[useServerAccess] Error fetching server access:', err);
      setError(err.message || 'Failed to fetch server access information');
    } finally {
      setLoading(false);
    }
  }, [userId, wallet?.credits, toolId]);

  // Check if user can access specific server type
  const checkServerAccess = useCallback((serverType) => {
    if (!currentPass) {
      return {
        allowed: serverType === 'slow',
        reason: serverType === 'fast' ? 'No active pass' : null
      };
    }

    const tier = currentPass.pass_type.tier;
    const category = currentPass.pass_type.category;
    const creditsBalance = wallet?.credits || 0;

    return canAccessServerType(tier, category, serverType, creditsBalance);
  }, [currentPass, wallet?.credits]);

  // Get server costs
  const getServerCosts = useCallback(() => {
    return {
      slow: getServerCostInfo('slow'),
      fast: getServerCostInfo('fast')
    };
  }, []);

  // Select optimal server for generation
  const selectOptimalServer = useCallback(async (preferredType = 'slow') => {
    if (!userId) {
      throw new Error('User not authenticated');
    }

    try {
      const result = await ServerAccessService.selectOptimalServer(
        userId,
        toolId,
        preferredType,
        currentPass,
        wallet?.credits || 0
      );

      return result;
    } catch (err) {
      console.error('[useServerAccess] Error selecting optimal server:', err);
      throw err;
    }
  }, [userId, toolId, currentPass, wallet?.credits]);

  // Validate access to specific server
  const validateServerAccess = useCallback(async (serverId) => {
    if (!userId || !serverId) {
      throw new Error('User ID and Server ID are required');
    }

    try {
      const result = await ServerAccessService.validateServerAccess(
        userId,
        serverId,
        currentPass,
        wallet?.credits || 0
      );

      return result;
    } catch (err) {
      console.error('[useServerAccess] Error validating server access:', err);
      throw err;
    }
  }, [userId, currentPass, wallet?.credits]);

  // Initial load and setup
  useEffect(() => {
    refreshServerAccess();
  }, [refreshServerAccess]);

  // Refresh when wallet credits change
  useEffect(() => {
    if (wallet) {
      refreshServerAccess();
    }
  }, [wallet?.credits, refreshServerAccess]);

  return {
    // Server access state
    allowedServers,
    availableServers,
    currentPass,
    serverRestrictions,
    loading,
    error,

    // Operations
    refreshServerAccess,
    checkServerAccess,
    getServerCosts,
    selectOptimalServer,
    validateServerAccess,

    // Computed values
    canAccessFast: allowedServers.includes('fast'),
    canAccessSlow: allowedServers.includes('slow'),
    hasServerRestrictions: Object.keys(serverRestrictions).length > 0,
    optimalServerType: allowedServers.includes('fast') && (wallet?.credits || 0) > 0 ? 'fast' : 'slow'
  };
};

export default useServerAccess;
