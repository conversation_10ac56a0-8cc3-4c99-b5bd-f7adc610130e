# Design System Documentation

## Overview

This design system provides a comprehensive set of reusable components and utilities for building consistent, accessible, and beautiful user interfaces. It's built on top of Material-UI (MUI) with custom theming and enhanced components.

## Design Tokens

### Spacing Scale
Based on a 4px unit system:
- `xs`: 4px
- `sm`: 8px  
- `md`: 16px
- `lg`: 24px
- `xl`: 32px
- `2xl`: 48px
- `3xl`: 64px

### Typography Scale
- `xs`: 12px (0.75rem)
- `sm`: 14px (0.875rem)
- `base`: 16px (1rem)
- `lg`: 18px (1.125rem)
- `xl`: 20px (1.25rem)
- `2xl`: 24px (1.5rem)
- `3xl`: 30px (1.875rem)
- `4xl`: 36px (2.25rem)
- `5xl`: 48px (3rem)

### Border Radius Scale
- `none`: 0
- `sm`: 4px
- `base`: 8px
- `md`: 12px
- `lg`: 16px
- `xl`: 24px
- `full`: 9999px

### Shadow Scale
- `xs`: Subtle shadow for small elements
- `sm`: Small shadow for cards and buttons
- `base`: Default shadow for elevated content
- `md`: Medium shadow for modals and dropdowns
- `lg`: Large shadow for prominent elements
- `xl`: Extra large shadow for major overlays
- `2xl`: Maximum shadow for hero elements

## Color System

### Primary Colors
- Green-based palette for primary actions
- 50-900 scale with proper contrast ratios

### Semantic Colors
- **Success**: Green variants for positive actions
- **Error**: Red variants for destructive actions
- **Warning**: Orange variants for caution
- **Info**: Blue variants for informational content

### Neutral Colors
- Comprehensive gray scale (50-900)
- Proper contrast ratios for accessibility
- Light and dark mode support

## Component Library

### Layout Components

#### Container
Responsive container with consistent max-widths and padding.

```jsx
import { Container } from '../components/design-system';

<Container maxWidth="1280px" centered>
  Content goes here
</Container>
```

#### Grid
Responsive grid system based on MUI Grid.

```jsx
import { Grid } from '../components/design-system';

<Grid.Container gap={2}>
  <Grid.Item xs={12} md={6}>
    Column 1
  </Grid.Item>
  <Grid.Item xs={12} md={6}>
    Column 2
  </Grid.Item>
</Grid.Container>
```

#### Stack
Flexible layout for vertical and horizontal arrangements.

```jsx
import { Stack } from '../components/design-system';

<Stack direction="column" spacing={3}>
  <div>Item 1</div>
  <div>Item 2</div>
</Stack>

<Stack.Horizontal spacing={2}>
  <button>Button 1</button>
  <button>Button 2</button>
</Stack.Horizontal>
```

### Typography Components

#### Heading
Semantic headings with consistent sizing.

```jsx
import { Heading } from '../components/design-system';

<Heading level="h1" size="4xl">
  Main Title
</Heading>

<Heading level="h2" size="2xl">
  Section Title
</Heading>
```

#### Text
Body text with size and variant options.

```jsx
import { Text } from '../components/design-system';

<Text size="base">
  Regular paragraph text
</Text>

<Text size="sm" variant="muted">
  Secondary text
</Text>
```

#### Label
Form labels with required indicators.

```jsx
import { Label } from '../components/design-system';

<Label htmlFor="email" required>
  Email Address
</Label>
```

### Interactive Components

#### Button
Enhanced buttons with variants and states.

```jsx
import { Button } from '../components/design-system';

<Button variant="contained" color="primary">
  Primary Action
</Button>

<Button variant="outlined">
  Secondary Action
</Button>

<Button variant="ghost">
  Subtle Action
</Button>

<Button.Icon ariaLabel="Settings">
  <SettingsIcon />
</Button.Icon>
```

#### Input
Form inputs with labels and validation.

```jsx
import { Input } from '../components/design-system';

<Input
  label="Email"
  type="email"
  placeholder="Enter your email"
  required
  helperText="We'll never share your email"
/>
```

#### Card
Content containers with hover effects.

```jsx
import { Card } from '../components/design-system';

<Card variant="elevated" interactive>
  <Card.Header title="Card Title" />
  <Card.Content>
    Card content goes here
  </Card.Content>
  <Card.Actions>
    <Button>Action</Button>
  </Card.Actions>
</Card>
```

### Feedback Components

#### Alert
Status messages with semantic colors.

```jsx
import { Alert } from '../components/design-system';

<Alert severity="success" closable>
  Operation completed successfully!
</Alert>

<Alert severity="error" title="Error">
  Something went wrong. Please try again.
</Alert>
```

#### Badge
Status indicators and labels.

```jsx
import { Badge } from '../components/design-system';

<Badge color="success">Active</Badge>

<Badge.Notification count={5}>
  <IconButton>
    <NotificationsIcon />
  </IconButton>
</Badge.Notification>
```

#### Loading
Loading states and skeletons.

```jsx
import { Loading } from '../components/design-system';

<Loading type="spinner" size="large" centered />

<Loading type="linear" text="Loading..." />

<Loading.Skeleton lines={3} avatar />
```

## Theme Utilities

### Usage
```jsx
import { 
  getSpacing, 
  getShadow, 
  getBorderRadius,
  createFocusRing 
} from '../utils/theme-utils';

const styles = {
  padding: getSpacing(theme, 2),
  boxShadow: getShadow('md'),
  borderRadius: getBorderRadius('lg'),
  '&:focus-visible': createFocusRing(theme.palette.primary.main)
};
```

## Accessibility

- All components follow WCAG 2.1 AA guidelines
- Proper focus management with visible focus indicators
- Semantic HTML structure
- Screen reader friendly
- Keyboard navigation support
- Color contrast compliance

## Responsive Design

- Mobile-first approach
- Consistent breakpoints (xs, sm, md, lg, xl)
- Responsive typography scaling
- Touch-friendly interface elements (44px minimum)
- Flexible grid system

## Best Practices

1. **Use design tokens**: Always use spacing, colors, and typography from the design system
2. **Consistent naming**: Follow the established naming conventions
3. **Accessibility first**: Ensure all components are accessible
4. **Performance**: Use React.memo and proper key props for lists
5. **Testing**: Write tests for custom components
6. **Documentation**: Document any custom props or usage patterns

## Migration Guide

When migrating existing components:

1. Replace hardcoded values with design tokens
2. Use design system components instead of custom ones
3. Update color usage to semantic colors
4. Ensure proper spacing using the spacing scale
5. Test accessibility and responsive behavior
