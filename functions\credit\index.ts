import { Request, Response } from 'express';
import { handleCreditTransaction } from './handleCreditTransaction';
import { handleDailyCredits } from './handleDailyCredits';

/**
 * Router function for credit-related endpoints
 */
export default async (req: Request, res: Response): Promise<void> => {
  const path = req.path;

  // Route to the appropriate handler based on the path
  if (path === '/transaction' || path === '/handleCreditTransaction') {
    await handleCreditTransaction(req, res);
  } else if (path === '/daily' || path === '/handleDailyCredits') {
    await handleDailyCredits(req, res);
  } else {
    res.status(404).json({
      success: false,
      message: 'Endpoint not found'
    });
  }
};

