// src/components/ProjectCarousel.jsx
import React, { useRef } from 'react';
import {
  Box,
  IconButton,
  Stack,
  useTheme
} from '@mui/material';

// Design System Components
import {
  Text
} from './design-system/index';

// Icons
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

/**
 * ProjectCarousel Component
 * Displays projects in a horizontal scrollable carousel layout
 */
function ProjectCarousel({ children, title, emptyMessage }) {
  const theme = useTheme();
  const scrollContainerRef = useRef(null);

  const scroll = (direction) => {
    if (scrollContainerRef.current) {
      const scrollAmount = 320; // Width of one card plus gap
      const currentScroll = scrollContainerRef.current.scrollLeft;
      const targetScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      scrollContainerRef.current.scrollTo({
        left: targetScroll,
        behavior: 'smooth'
      });
    }
  };

  const hasItems = React.Children.count(children) > 0;
  const showNavigation = React.Children.count(children) > 1;

  return (
    <Box sx={{ position: 'relative' }}>
      {/* Navigation Arrows */}
      {showNavigation && (
        <>
          <IconButton
            onClick={() => scroll('left')}
            sx={{
              position: 'absolute',
              left: -20,
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2,
              backgroundColor: 'background.paper',
              border: `1px solid ${theme.palette.divider}`,
              boxShadow: theme.shadows[2],
              '&:hover': {
                backgroundColor: 'action.hover',
              },
              display: { xs: 'none', sm: 'flex' }
            }}
            size="small"
          >
            <ChevronLeftIcon />
          </IconButton>
          
          <IconButton
            onClick={() => scroll('right')}
            sx={{
              position: 'absolute',
              right: -20,
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2,
              backgroundColor: 'background.paper',
              border: `1px solid ${theme.palette.divider}`,
              boxShadow: theme.shadows[2],
              '&:hover': {
                backgroundColor: 'action.hover',
              },
              display: { xs: 'none', sm: 'flex' }
            }}
            size="small"
          >
            <ChevronRightIcon />
          </IconButton>
        </>
      )}

      {/* Carousel Container */}
      <Box
        ref={scrollContainerRef}
        sx={{
          display: 'flex',
          gap: 2,
          overflowX: 'auto',
          overflowY: 'hidden',
          scrollBehavior: 'smooth',
          pb: 1,
          // Hide scrollbar on desktop, show on mobile for touch scrolling
          '&::-webkit-scrollbar': {
            height: { xs: 8, sm: 0 },
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.divider,
            borderRadius: 4,
          },
          '&::-webkit-scrollbar-thumb:hover': {
            backgroundColor: theme.palette.action.hover,
          },
          // For Firefox
          scrollbarWidth: { xs: 'thin', sm: 'none' },
          scrollbarColor: `${theme.palette.divider} transparent`,
        }}
      >
        {hasItems ? (
          React.Children.map(children, (child, index) => (
            <Box
              key={index}
              sx={{
                minWidth: { xs: 280, sm: 300 },
                maxWidth: { xs: 280, sm: 300 },
                flexShrink: 0,
              }}
            >
              {child}
            </Box>
          ))
        ) : (
          <Stack 
            alignItems="center" 
            justifyContent="center"
            sx={{ 
              width: '100%', 
              minHeight: 200,
              textAlign: 'center',
              py: 4
            }}
          >
            <Text variant="muted" size="md">
              {emptyMessage || 'No items found.'}
            </Text>
          </Stack>
        )}
      </Box>

      {/* Mobile scroll indicator */}
      {showNavigation && (
        <Box 
          sx={{ 
            display: { xs: 'flex', sm: 'none' },
            justifyContent: 'center',
            mt: 1,
            gap: 0.5
          }}
        >
          <Text size="xs" variant="muted">
            ← Swipe to see more →
          </Text>
        </Box>
      )}
    </Box>
  );
}

export default ProjectCarousel;
