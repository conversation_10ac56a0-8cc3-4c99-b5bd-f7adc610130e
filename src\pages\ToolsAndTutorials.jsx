// src/pages/ToolsAndTutorials.jsx
import React from 'react';
import { Grid, CardContent, CardMedia } from '@mui/material';
import ToolCard from '../components/ToolCard'; // Ensure ToolCard styles align
import tools from '../components/Tools'; // Tool data

// Design System Components
import {
    Container,
    Stack,
    Heading,
    Text,
    Button,
    Card
} from '../components/design-system/index';

function ToolsAndTutorials() {
  return (
    <Container maxWidth="1280px" sx={{ py: { xs: 2, sm: 4 } }}>
      <Stack spacing={5}>
        {/* Page Title */}
        <Stack spacing={1} sx={{ textAlign: { xs: 'center', sm: 'left'} }}>
          <Heading level="h1" size="4xl">
            Tools & Tutorials
          </Heading>
          <Text size="lg" variant="muted">
            Explore available AI tools and learn how to use them effectively.
          </Text>
        </Stack>

        {/* Available Tools Section */}
        <Stack spacing={3}>
          <Heading level="h2" size="2xl" sx={{ textAlign: { xs: 'center', sm: 'left'} }}>
            Available Tools
          </Heading>
      <Grid container spacing={{ xs: 2, md: 3 }} sx={{ mb: 6 }}>
        {tools.map((tool) => (
          <Grid item xs={12} sm={6} lg={4} key={tool.id}> {/* Adjusted grid breakpoints */}
            {/* ToolCard inherits MuiCard styles from theme */}
            <ToolCard tool={tool} />
          </Grid>
        ))}
        {tools.length === 0 && (
          <Grid item xs={12}>
            <Text variant="muted">No tools available.</Text>
          </Grid>
        )}
      </Grid>
        </Stack>

        {/* Tutorials Section */}
        <Stack spacing={3}>
          <Heading level="h2" size="2xl" sx={{ textAlign: { xs: 'center', sm: 'left'} }}>
            Tutorials
          </Heading>
      <Grid container spacing={{ xs: 2, md: 3 }} sx={{ mb: 6 }}>
        <Grid item xs={12} sm={6} lg={4}>
          {/* Card style from theme */}
          <Card>
            <CardMedia
              component="img" height="160"
              image="https://via.placeholder.com/345x160.png?text=Tutorial+1"
              alt="Tutorial 1"
            />
            <CardContent>
              <Heading level="h3" size="lg" sx={{ mb: 1 }}>
                Getting Started with ReImagine
              </Heading>
              <Button variant="outlined" size="small" aria-label="Watch tutorial on ReImagine" sx={{ mt: 1 }}>
                Watch Now
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} lg={4}>
          <Card>
            <CardMedia component="img" height="160" image="https://via.placeholder.com/345x160.png?text=Tutorial+2" alt="Tutorial 2"/>
            <CardContent>
              <Heading level="h3" size="lg" sx={{ mb: 1 }}>
                Creating Videos Efficiently
              </Heading>
              <Button variant="outlined" size="small" aria-label="Watch tutorial on Image to Video" sx={{ mt: 1 }}>
                Watch Now
              </Button>
            </CardContent>
          </Card>
        </Grid>
        {/* Add more tutorial cards */}
      </Grid>
        </Stack>
      </Stack>
    </Container>
  );
}

export default ToolsAndTutorials;
