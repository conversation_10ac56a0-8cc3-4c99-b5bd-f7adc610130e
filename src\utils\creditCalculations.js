// src/utils/creditCalculations.js
// Credit calculation utilities for the unified credit system

/**
 * Calculate daily free credits based on user tier
 * @param {string} userTier - User's current pass tier ('Free', 'Paid', 'Lite', 'Pro', 'Elite')
 * @param {string} userCategory - User's pass category ('Visitor', 'Citizen')
 * @returns {number} Daily free credits amount
 */
export function calculateDailyFreeCredits(userTier, userCategory) {
  // Only Visitor Free tier gets daily free credits
  if (userCategory === 'Visitor' && userTier === 'Free') {
    return 3600; // 1 hour = 3600 seconds = 3600 credits
  }
  return 0;
}

/**
 * Calculate usage percentage of credits
 * @param {number} used - Credits used
 * @param {number} total - Total credits available
 * @returns {number} Usage percentage (0-100)
 */
export function calculateUsagePercentage(used, total) {
  if (total === 0) return 0;
  return Math.min(100, Math.max(0, (used / total) * 100));
}

/**
 * Calculate generation cost in credits
 * @param {number} seconds - Duration in seconds
 * @param {number} costPerSecond - Cost per second (always 1 for unified system)
 * @returns {number} Total cost in credits
 */
export function calculateGenerationCost(seconds, costPerSecond = 1) {
  return Math.ceil(seconds * costPerSecond);
}

/**
 * Format credits to time display
 * @param {number} credits - Number of credits
 * @returns {string} Formatted time string (e.g., "1h 30m", "45s")
 */
export function formatCreditsToTime(credits) {
  if (credits < 60) {
    return `${credits}s`;
  }
  
  const hours = Math.floor(credits / 3600);
  const minutes = Math.floor((credits % 3600) / 60);
  const seconds = credits % 60;
  
  const parts = [];
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (seconds > 0 && hours === 0) parts.push(`${seconds}s`);
  
  return parts.join(' ') || '0s';
}

/**
 * Format time to credits
 * @param {number} seconds - Time in seconds
 * @returns {number} Credits equivalent
 */
export function formatTimeToCredits(seconds) {
  return Math.ceil(seconds);
}

/**
 * Check if user can claim free credits
 * @param {Object} wallet - User wallet object
 * @param {string} userTier - User's current tier
 * @param {string} userCategory - User's category
 * @returns {Object} Eligibility result
 */
export function canClaimFreeCredits(wallet, userTier, userCategory) {
  const dailyCredits = calculateDailyFreeCredits(userTier, userCategory);
  
  if (dailyCredits === 0) {
    return {
      eligible: false,
      reason: 'No daily credits available for this tier'
    };
  }
  
  // Check if 24 hours have passed since last claim
  if (wallet.last_free_credit_claim) {
    const lastClaim = new Date(wallet.last_free_credit_claim);
    const now = new Date();
    const hoursSinceLastClaim = (now - lastClaim) / (1000 * 60 * 60);
    
    if (hoursSinceLastClaim < 24) {
      const hoursRemaining = Math.ceil(24 - hoursSinceLastClaim);
      return {
        eligible: false,
        reason: `Must wait ${hoursRemaining} hours before next claim`,
        hoursRemaining
      };
    }
  }
  
  // Check if user has less than 70% of daily credits remaining
  const usagePercentage = calculateUsagePercentage(wallet.credits_balance, dailyCredits);
  if (usagePercentage >= 70) {
    return {
      eligible: false,
      reason: 'Must use more credits before claiming (need <70% remaining)',
      currentPercentage: usagePercentage
    };
  }
  
  return {
    eligible: true,
    creditsToGrant: dailyCredits - wallet.credits_balance
  };
}

/**
 * Calculate real-time generation cost
 * @param {number} elapsedMs - Elapsed time in milliseconds
 * @param {number} costPerSecond - Cost per second
 * @returns {number} Current cost in credits
 */
export function calculateRealTimeCost(elapsedMs, costPerSecond = 1) {
  const elapsedSeconds = elapsedMs / 1000;
  return Math.ceil(elapsedSeconds * costPerSecond);
}

/**
 * Validate credit deduction parameters
 * @param {number} credits - Credits to deduct
 * @param {number} availableCredits - Available credits
 * @returns {Object} Validation result
 */
export function validateCreditDeduction(credits, availableCredits) {
  if (credits <= 0) {
    return {
      valid: false,
      error: 'Credits must be positive'
    };
  }
  
  if (credits > availableCredits) {
    return {
      valid: false,
      error: 'Insufficient credits',
      required: credits,
      available: availableCredits,
      shortfall: credits - availableCredits
    };
  }
  
  return { valid: true };
}

/**
 * Calculate credits needed for estimated duration
 * @param {number} estimatedSeconds - Estimated duration in seconds
 * @param {string} serverType - Server type ('slow' or 'fast')
 * @returns {number} Credits needed
 */
export function calculateCreditsNeeded(estimatedSeconds, serverType = 'slow') {
  // In unified system, 1 credit = 1 second regardless of server type
  return Math.ceil(estimatedSeconds);
}

/**
 * Format credit balance display
 * @param {number} credits - Credit balance
 * @returns {Object} Formatted display object
 */
export function formatCreditBalance(credits) {
  return {
    credits,
    timeDisplay: formatCreditsToTime(credits),
    shortDisplay: credits >= 3600 ? 
      `${Math.floor(credits / 3600)}h ${Math.floor((credits % 3600) / 60)}m` :
      formatCreditsToTime(credits)
  };
}
