import { Request, Response } from 'express';
import { gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import { nhost } from '../_utils/nhost';

/**
 * Unified Credit System - Generation Credit Deduction
 * Handles credit deduction for generation based on time consumed (1 credit = 1 second)
 */

interface GenerationCreditRequest {
  userId: string;
  seconds: number;
  serverType: 'slow' | 'fast';
  generationId: string;
  reason?: string;
}

interface GenerationCreditResponse {
  success: boolean;
  message?: string;
  credits_deducted?: number;
  balance_after?: number;
  error?: string;
  credits_needed?: number;
  credits_available?: number;
}

// GraphQL Queries for Unified Credit System
const GET_USER_WALLET_UNIFIED = gql`
  query GetUserWalletUnified($userId: uuid!) {
    user_wallet(where: { user_id: { _eq: $userId } }) {
      id
      user_id
      credits_balance
      last_free_credit_claim
      daily_free_credits_granted
      total_credits_purchased
      active_pass_id
      created_at
      updated_at
    }
    user_passes(
      where: {
        user_id: { _eq: $userId },
        status: { _eq: "active" },
        _or: [
          { end_date: { _is_null: true } },
          { end_date: { _gte: "now()" } }
        ]
      }
    ) {
      id
      user_id
      pass_type_id
      start_date
      end_date
      status
      hours_used
      allowed_server_types
      auto_downgraded_from
      pass_type {
        id
        name
        tier
        category
        monthly_price
        hours_included
        is_unlimited
        slow_generation_limit
        fast_generation_limit
        features
      }
    }
  }
`;

const UPDATE_USER_WALLET_UNIFIED = gql`
  mutation UpdateUserWalletUnified(
    $walletId: uuid!,
    $creditsBalance: Int!
  ) {
    update_user_wallet_by_pk(
      pk_columns: { id: $walletId },
      _set: {
        credits_balance: $creditsBalance,
        updated_at: "now()"
      }
    ) {
      id
      credits_balance
      updated_at
    }
  }
`;

const INSERT_CREDIT_TRANSACTION_UNIFIED = gql`
  mutation InsertCreditTransactionUnified(
    $id: uuid!,
    $userId: uuid!,
    $changeAmount: Int!,
    $balanceAfter: Int!,
    $transactionType: String!,
    $serverType: String!,
    $secondsConsumed: Int!,
    $costPerSecond: numeric!,
    $reason: String!,
    $relatedId: String
  ) {
    insert_credit_transactions_one(
      object: {
        id: $id,
        user_id: $userId,
        change_amount: $changeAmount,
        balance_after: $balanceAfter,
        transaction_type: $transactionType,
        server_type: $serverType,
        seconds_consumed: $secondsConsumed,
        cost_per_second: $costPerSecond,
        reason: $reason,
        related_id: $relatedId,
        created_at: "now()"
      }
    ) {
      id
      created_at
    }
  }
`;

/**
 * Handle generation credit deduction for unified credit system
 */
export const handleGenerationCreditDeduction = async (
  req: Request & { body: GenerationCreditRequest },
  res: Response
): Promise<Response | void> => {
  try {
    const { userId, seconds, serverType, generationId, reason } = req.body;

    console.log(`[DeductGeneration] 💳 Processing credit deduction for user ${userId}: ${seconds}s on ${serverType} server`);

    // Validate input
    if (!userId || !seconds || !serverType || !generationId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: userId, seconds, serverType, generationId'
      });
    }

    if (seconds <= 0) {
      return res.status(400).json({
        success: false,
        error: 'Seconds must be positive'
      });
    }

    if (!['slow', 'fast'].includes(serverType)) {
      return res.status(400).json({
        success: false,
        error: 'Server type must be slow or fast'
      });
    }

    // Calculate credits needed (1 credit = 1 second in unified system)
    const creditsNeeded = Math.ceil(seconds);

    // Get user wallet and pass information
    const { data: walletData, error: walletError } = await nhost.graphql.request(GET_USER_WALLET_UNIFIED, {
      userId
    });

    if (walletError) {
      console.error('[DeductGeneration] ❌ GraphQL error fetching wallet:', walletError);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch wallet information'
      });
    }

    if (!walletData?.user_wallet?.[0]) {
      return res.status(404).json({
        success: false,
        error: 'Wallet not found'
      });
    }

    const wallet = walletData.user_wallet[0];
    const activePasses = walletData.user_passes || [];
    const activePass = activePasses.find(pass =>
      pass.status === 'active' &&
      (!pass.end_date || new Date(pass.end_date) > new Date())
    );

    // Check if user has unlimited usage
    const hasUnlimitedUsage = activePass?.pass_type?.is_unlimited || false;

    if (hasUnlimitedUsage) {
      console.log('[DeductGeneration] ✅ User has unlimited usage, no credit deduction needed');
      
      // Still log the transaction for tracking
      const transactionId = uuidv4();
      await nhost.graphql.request(INSERT_CREDIT_TRANSACTION_UNIFIED, {
        id: transactionId,
        userId,
        changeAmount: 0, // No change for unlimited users
        balanceAfter: wallet.credits_balance,
        transactionType: 'deduction',
        serverType,
        secondsConsumed: Math.ceil(seconds),
        costPerSecond: 1.0,
        reason: reason || `Generation credit deduction (${serverType} server) - Unlimited user`,
        relatedId: generationId
      });

      return res.status(200).json({
        success: true,
        message: 'Generation completed with unlimited usage',
        credits_deducted: 0,
        balance_after: wallet.credits_balance
      });
    }

    // Check if user has sufficient credits
    const currentBalance = wallet.credits_balance || 0;
    if (currentBalance < creditsNeeded) {
      console.log(`[DeductGeneration] ❌ Insufficient credits: need ${creditsNeeded}, have ${currentBalance}`);
      return res.status(400).json({
        success: false,
        error: 'Insufficient credits',
        credits_needed: creditsNeeded,
        credits_available: currentBalance
      });
    }

    // Deduct credits
    const newBalance = currentBalance - creditsNeeded;

    // Update wallet balance
    const { error: updateError } = await nhost.graphql.request(UPDATE_USER_WALLET_UNIFIED, {
      walletId: wallet.id,
      creditsBalance: newBalance
    });

    if (updateError) {
      console.error('[DeductGeneration] ❌ Failed to update wallet:', updateError);
      return res.status(500).json({
        success: false,
        error: 'Failed to update wallet balance'
      });
    }

    // Log transaction
    const transactionId = uuidv4();
    const { error: transactionError } = await nhost.graphql.request(INSERT_CREDIT_TRANSACTION_UNIFIED, {
      id: transactionId,
      userId,
      changeAmount: -creditsNeeded,
      balanceAfter: newBalance,
      transactionType: 'deduction',
      serverType,
      secondsConsumed: Math.ceil(seconds),
      costPerSecond: 1.0,
      reason: reason || `Generation credit deduction (${serverType} server)`,
      relatedId: generationId
    });

    if (transactionError) {
      console.error('[DeductGeneration] ⚠️ Failed to log transaction:', transactionError);
      // Don't fail the request, just log the error
    }

    console.log(`[DeductGeneration] ✅ Successfully deducted ${creditsNeeded} credits. New balance: ${newBalance}`);

    // Check if user should be auto-downgraded (Visitor Paid with 0 credits)
    if (newBalance === 0 && activePass?.pass_type?.tier === 'Paid' && activePass?.pass_type?.category === 'Visitor') {
      console.log('[DeductGeneration] 🔄 User should be auto-downgraded to free tier');
      // Note: Auto-downgrade logic would be handled by a separate function
    }

    return res.status(200).json({
      success: true,
      message: 'Credits deducted successfully',
      credits_deducted: creditsNeeded,
      balance_after: newBalance
    });

  } catch (error) {
    console.error('[DeductGeneration] 💥 Unexpected error:', error);
    return res.status(500).json({
      success: false,
      error: 'An unexpected error occurred during credit deduction'
    });
  }
};

/**
 * Express handler for generation credit deduction
 */
export default async (req: Request, res: Response): Promise<Response | void> => {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  await handleGenerationCreditDeduction(req, res);
};
