// src/components/UsageStatistics.jsx
import React from 'react';
import {
  Paper,
  Grid,
  Typography,
  Box,
  Skeleton
} from '@mui/material';

// Design System Components
import {
  Text,
  Heading
} from './design-system/index';

// Icons
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CreditScoreIcon from '@mui/icons-material/CreditScore';

/**
 * UsageStatistics Component
 * Displays user's usage statistics including generations, credits used, and hours
 */
function UsageStatistics({ walletInfo, passInfo, isLoading = false }) {
  if (isLoading) {
    return (
      <Paper elevation={0} sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2, height: '100%' }}>
        <Box sx={{ mb: 2 }}>
          <Skeleton variant="text" width="60%" height={24} />
        </Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Skeleton variant="rectangular" height={60} />
          </Grid>
          <Grid item xs={12}>
            <Skeleton variant="rectangular" height={60} />
          </Grid>
          <Grid item xs={12}>
            <Skeleton variant="rectangular" height={60} />
          </Grid>
        </Grid>
      </Paper>
    );
  }

  const stats = [
    {
      label: 'Generations Today',
      value: walletInfo?.stats?.generationsToday || 0,
      icon: <TrendingUpIcon sx={{ fontSize: '1.2rem', color: 'primary.main' }} />,
      color: 'primary.main'
    },
    {
      label: 'Credits Used Today',
      value: walletInfo?.stats?.creditsUsedToday || 0,
      icon: <CreditScoreIcon sx={{ fontSize: '1.2rem', color: 'secondary.main' }} />,
      color: 'secondary.main'
    },
    {
      label: 'Hours Used',
      value: passInfo?.activePass?.hours_used || 0,
      suffix: passInfo?.activePass?.pass_type?.hours_included ? 
        `/${passInfo.activePass.pass_type.hours_included}` : '',
      icon: <AccessTimeIcon sx={{ fontSize: '1.2rem', color: 'success.main' }} />,
      color: 'success.main'
    }
  ];

  return (
    <Paper elevation={0} sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2, height: '100%' }}>
      <Box sx={{ mb: 3 }}>
        <Heading level="h3" size="lg" weight={600}>
          Usage Statistics
        </Heading>
        <Text size="sm" variant="muted">
          Your activity overview
        </Text>
      </Box>
      
      <Grid container spacing={2}>
        {stats.map((stat, index) => (
          <Grid item xs={12} key={index}>
            <Box 
              sx={{ 
                p: 2, 
                border: '1px solid', 
                borderColor: 'divider', 
                borderRadius: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                transition: 'all 0.2s ease',
                '&:hover': {
                  borderColor: stat.color,
                  backgroundColor: 'action.hover'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {stat.icon}
                <Box>
                  <Text size="sm" variant="muted">
                    {stat.label}
                  </Text>
                </Box>
              </Box>
              <Typography 
                variant="h6" 
                sx={{ 
                  fontWeight: 600,
                  color: stat.color
                }}
              >
                {stat.value}{stat.suffix}
              </Typography>
            </Box>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
}

export default UsageStatistics;
