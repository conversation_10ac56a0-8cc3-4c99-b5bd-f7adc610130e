// src/components/design-system/Layout/Grid.jsx
import React from 'react';
import { Grid as MuiGrid } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledGrid = styled(MuiGrid)(({ theme, gap }) => ({
  ...(gap && {
    gap: theme.spacing(gap),
  }),
}));

/**
 * Grid component for responsive layouts
 * 
 * @param {Object} props
 * @param {number} props.gap - Gap between grid items (in spacing units)
 * @param {number} props.xs - Grid size for xs breakpoint
 * @param {number} props.sm - Grid size for sm breakpoint
 * @param {number} props.md - Grid size for md breakpoint
 * @param {number} props.lg - Grid size for lg breakpoint
 * @param {number} props.xl - Grid size for xl breakpoint
 * @param {React.ReactNode} props.children - Child components
 */
const Grid = ({ 
  gap,
  children,
  ...props 
}) => {
  return (
    <StyledGrid
      gap={gap}
      {...props}
    >
      {children}
    </StyledGrid>
  );
};

// Grid container component
const GridContainer = ({ gap = 2, children, ...props }) => {
  return (
    <Grid
      container
      gap={gap}
      {...props}
    >
      {children}
    </Grid>
  );
};

// Grid item component
const GridItem = ({ children, ...props }) => {
  return (
    <Grid
      item
      {...props}
    >
      {children}
    </Grid>
  );
};

Grid.Container = GridContainer;
Grid.Item = GridItem;

export default Grid;
