// Static data defining the available AI tools in the application

const tools = [
  {
    id: 'reimagine', // Unique identifier, used in routing (e.g., /tool/reimagine)
    title: 'ReImagine',
    description: 'Transform images with creative prompts using ReImagine.',
    // Placeholder image - Replace with actual relevant image URL later
    previewImage: 'https://via.placeholder.com/345x140.png?text=ReImagine+Tool',
    // Note: workflowUrl is not used by ToolCard, but kept for potential future reference
    // workflowUrl: 'workflows/Imagen1.json', // Workflow path in Firebase Storage
  },
  {
    id: 'img2video', // Unique identifier for the Image to Video tool
    title: 'Image to Video',
    description: 'Convert static images into short animated videos.',
    // Placeholder image - Replace with actual relevant image URL later
    previewImage: 'https://via.placeholder.com/345x140.png?text=Image+to+Video+Tool',
    // workflowUrl: 'workflows/Videogen1.json', // Workflow path in Firebase Storage
  },
  // --- Future Tools Example ---
  // {
  //   id: 'text-to-image',
  //   title: 'Text to Image',
  //   description: 'Generate images from text descriptions.',
  //   previewImage: 'https://via.placeholder.com/345x140.png?text=Text+to+Image',
  //   // workflowUrl: 'workflows/TextToImage_v1.json',
  // },
  // {
  //   id: 'motion-brush',
  //   title: 'Multi Motion Brush',
  //   description: 'Add motion to specific areas of an image.',
  //   previewImage: 'https://via.placeholder.com/345x140.png?text=Motion+Brush',
  //   // workflowUrl: 'workflows/MotionBrush_v1.json',
  // },
  // ... Add definitions for up to 70+ tools here following the same structure
];

export default tools; // Export the array of tool definitions