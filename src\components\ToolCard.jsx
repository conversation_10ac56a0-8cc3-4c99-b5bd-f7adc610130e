// src/components/ToolCard.jsx
import React from 'react';
// Use CardActionArea for whole card link
import { Card, CardActionArea, CardContent, CardMedia, Typography, Box } from '@mui/material';
import { Link } from 'react-router-dom'; // Use react-router Link

// RichUI reference Icon SVG (Example for Products)
const ProductIcon = () => (
     <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.2" /*...*/ >
       <path d="M14.5 3.5C14.5 3.5 14.5 5.5 12 5.5C9.5 5.5 9.5 3.5 9.5 3.5H7.5L4.20711 6.79289C3.81658 7.18342 3.81658 7.81658 4.20711 8.20711L6.5 10.5V20.5H17.5V10.5L19.7929 8.20711C20.1834 7.81658 20.1834 7.18342 19.7929 6.79289L16.5 3.5H14.5Z"/>
     </svg>
);

function ToolCard({ tool }) {
  const { id, title, description, previewImage, icon } = tool; // Assume optional 'icon' string for lookup later

  // Placeholder icon lookup (replace with actual logic if needed)
  const CardIconComponent = icon === 'product' ? ProductIcon : null; // Example

  return (
    // Card styles primarily from theme. Set height 100% for grid layout.
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Make entire card area clickable, link to tool page */}
      <CardActionArea
            component={Link}
            to={`/tool/${id}`}
            disabled={!id}
            sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1, p: 2 }} // Adjust padding here
            aria-label={`Use ${title} tool`}
        >
           {/* Icon Area (Inspired by RichUI) */}
            {/* For simplicity, maybe don't replicate the exact border/bg effect via CSS
                 just place an icon if available */}
             <Box sx={{ mb: 2, color: 'primary.main', width: 32, height: 32 }}>
                {/* Add logic to render actual tool icon based on 'icon' prop? */}
                 { CardIconComponent ? <CardIconComponent /> : <ProductIcon /> }
             </Box>

           {/* Content */}
            <CardContent sx={{ flexGrow: 1, p: 0 }}> {/* Remove default CardContent padding */}
                <Typography gutterBottom variant="h5" component="h4" sx={{ color: 'text.primary', fontWeight: 600 }}> {/* Use h4 for semantics */}
                 {title || 'Unnamed Tool'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                 {description || 'No description.'}
                </Typography>
           </CardContent>
            {/* Removing the separate button as whole card is clickable */}
            {/* <CardActions sx={{ px: 2, pb: 2, pt: 0 }}>
                 <Button variant="contained" size="small"> Use Tool </Button>
             </CardActions> */}
       </CardActionArea>
    </Card>
  );
}

export default ToolCard;