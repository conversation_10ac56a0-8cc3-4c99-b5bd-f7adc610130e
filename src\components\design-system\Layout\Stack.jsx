// src/components/design-system/Layout/Stack.jsx
import React from 'react';
import { Stack as MuiStack } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledStack = styled(MuiStack)(({ theme }) => ({
  // Additional custom styling if needed
}));

/**
 * Stack component for vertical and horizontal layouts with consistent spacing
 * 
 * @param {Object} props
 * @param {'row'|'column'} props.direction - Direction of the stack
 * @param {number} props.spacing - Spacing between items (in spacing units)
 * @param {'flex-start'|'center'|'flex-end'|'stretch'|'baseline'} props.alignItems - Align items
 * @param {'flex-start'|'center'|'flex-end'|'space-between'|'space-around'|'space-evenly'} props.justifyContent - Justify content
 * @param {boolean} props.divider - Whether to show dividers between items
 * @param {React.ReactNode} props.children - Child components
 */
const Stack = ({ 
  direction = 'column',
  spacing = 2,
  alignItems,
  justifyContent,
  divider,
  children,
  ...props 
}) => {
  return (
    <StyledStack
      direction={direction}
      spacing={spacing}
      alignItems={alignItems}
      justifyContent={justifyContent}
      divider={divider}
      {...props}
    >
      {children}
    </StyledStack>
  );
};

// Horizontal stack shorthand
const HStack = ({ children, ...props }) => {
  return (
    <Stack
      direction="row"
      {...props}
    >
      {children}
    </Stack>
  );
};

// Vertical stack shorthand
const VStack = ({ children, ...props }) => {
  return (
    <Stack
      direction="column"
      {...props}
    >
      {children}
    </Stack>
  );
};

Stack.Horizontal = HStack;
Stack.Vertical = VStack;

export default Stack;
