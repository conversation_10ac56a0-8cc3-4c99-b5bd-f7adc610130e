// src/hooks/usePassManagement.js
// Pass state and operations management hook for the unified credit system

import { useState, useEffect, useCallback } from 'react';
import { useUserId } from '@nhost/react';
import { PassService } from '../services/passService';
import { shouldAutoDowngrade, validatePassUpgrade } from '../utils/passUtils';
import { useCreditWallet } from './useCreditWallet';

/**
 * Hook for managing pass state and operations
 * @returns {Object} Pass state and operations
 */
export const usePassManagement = () => {
  const userId = useUserId();
  const { wallet } = useCreditWallet();
  const [currentPass, setCurrentPass] = useState(null);
  const [availablePasses, setAvailablePasses] = useState([]);
  const [passHistory, setPassHistory] = useState([]);
  const [autoDowngradeStatus, setAutoDowngradeStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch current pass information
  const refreshCurrentPass = useCallback(async () => {
    if (!userId) {
      setCurrentPass(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const passInfo = await PassService.getUserActivePass(userId);
      setCurrentPass(passInfo.activePass);

      // Check auto-downgrade status if user has credits info
      if (passInfo.activePass && wallet) {
        const downgradeCheck = shouldAutoDowngrade(
          wallet.credits,
          passInfo.activePass.pass_type.tier,
          passInfo.activePass.pass_type.category
        );
        
        if (downgradeCheck) {
          setAutoDowngradeStatus({
            shouldDowngrade: true,
            reason: 'Credits exhausted',
            currentPassId: passInfo.activePass.id
          });
        } else {
          setAutoDowngradeStatus(null);
        }
      }

    } catch (err) {
      console.error('[usePassManagement] Error fetching current pass:', err);
      setError(err.message || 'Failed to fetch current pass');
    } finally {
      setLoading(false);
    }
  }, [userId, wallet?.credits]);

  // Fetch available passes for upgrade
  const refreshAvailablePasses = useCallback(async () => {
    try {
      const passes = await PassService.getAvailablePasses();
      setAvailablePasses(passes);
    } catch (err) {
      console.error('[usePassManagement] Error fetching available passes:', err);
      setError(err.message || 'Failed to fetch available passes');
    }
  }, []);

  // Upgrade user pass
  const upgradePass = useCallback(async (newPassId) => {
    if (!userId || !newPassId) {
      throw new Error('User ID and new pass ID are required');
    }

    if (!currentPass) {
      throw new Error('No current pass found');
    }

    try {
      setLoading(true);

      // Find the target pass
      const targetPass = availablePasses.find(pass => pass.id === newPassId);
      if (!targetPass) {
        throw new Error('Target pass not found');
      }

      // Validate upgrade
      const validation = validatePassUpgrade(currentPass, targetPass);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Perform upgrade
      const result = await PassService.upgradePass(userId, newPassId);
      
      // Refresh current pass after upgrade
      await refreshCurrentPass();
      
      return result;
    } catch (err) {
      console.error('[usePassManagement] Error upgrading pass:', err);
      setError(err.message || 'Failed to upgrade pass');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [userId, currentPass, availablePasses, refreshCurrentPass]);

  // Check pass status (expiry, auto-downgrade, etc.)
  const checkPassStatus = useCallback(async () => {
    if (!userId) {
      return null;
    }

    try {
      const [expiryCheck, autoDowngradeCheck] = await Promise.all([
        PassService.checkPassExpiry(userId),
        PassService.checkAutoDowngrade(userId, wallet?.credits || 0)
      ]);

      return {
        expiry: expiryCheck,
        autoDowngrade: autoDowngradeCheck
      };
    } catch (err) {
      console.error('[usePassManagement] Error checking pass status:', err);
      throw err;
    }
  }, [userId, wallet?.credits]);

  // Handle auto-downgrade
  const handleAutoDowngrade = useCallback(async () => {
    if (!userId || !autoDowngradeStatus?.shouldDowngrade) {
      throw new Error('Auto-downgrade not applicable');
    }

    try {
      setLoading(true);
      
      const result = await PassService.autoDowngradePass(userId);
      
      // Refresh current pass after downgrade
      await refreshCurrentPass();
      setAutoDowngradeStatus(null);
      
      return result;
    } catch (err) {
      console.error('[usePassManagement] Error during auto-downgrade:', err);
      setError(err.message || 'Failed to auto-downgrade pass');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [userId, autoDowngradeStatus, refreshCurrentPass]);

  // Get upgrade suggestions
  const getUpgradeSuggestions = useCallback(() => {
    if (!currentPass) {
      return [];
    }

    const currentTier = currentPass.pass_type.tier;
    const currentCategory = currentPass.pass_type.category;

    return availablePasses.filter(pass => {
      // Filter out current pass and lower tiers
      if (pass.id === currentPass.pass_type_id) {
        return false;
      }

      // Suggest upgrades within same category or to Citizen
      if (pass.category === currentCategory) {
        return pass.tier !== currentTier;
      }

      if (currentCategory === 'Visitor' && pass.category === 'Citizen') {
        return true;
      }

      return false;
    }).sort((a, b) => a.sort_order - b.sort_order);
  }, [currentPass, availablePasses]);

  // Get pass features comparison
  const getPassFeatures = useCallback((passId) => {
    const pass = availablePasses.find(p => p.id === passId);
    if (!pass) return null;

    return {
      ...pass,
      features: pass.features || {},
      serverAccess: pass.category === 'Visitor' && pass.tier === 'Free' ? ['slow'] : ['slow', 'fast'],
      dailyCredits: pass.tier === 'Free' ? 3600 : 0,
      unlimited: pass.is_unlimited || false
    };
  }, [availablePasses]);

  // Initial load
  useEffect(() => {
    refreshCurrentPass();
    refreshAvailablePasses();
  }, [refreshCurrentPass, refreshAvailablePasses]);

  // Refresh when wallet changes (for auto-downgrade checks)
  useEffect(() => {
    if (wallet && currentPass) {
      refreshCurrentPass();
    }
  }, [wallet?.credits, refreshCurrentPass]);

  return {
    // Pass state
    currentPass,
    availablePasses,
    passHistory,
    autoDowngradeStatus,
    loading,
    error,

    // Operations
    refreshCurrentPass,
    refreshAvailablePasses,
    upgradePass,
    checkPassStatus,
    handleAutoDowngrade,

    // Computed values
    getUpgradeSuggestions,
    getPassFeatures,
    upgradeSuggestions: getUpgradeSuggestions(),
    
    // Status flags
    hasActivePass: !!currentPass,
    isFreeTier: currentPass?.pass_type?.tier === 'Free',
    isPaidTier: currentPass?.pass_type?.tier === 'Paid',
    isCitizenTier: currentPass?.pass_type?.category === 'Citizen',
    needsAutoDowngrade: autoDowngradeStatus?.shouldDowngrade || false,
    isUnlimited: currentPass?.pass_type?.is_unlimited || false
  };
};

export default usePassManagement;
