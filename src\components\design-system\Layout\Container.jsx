// src/components/design-system/Layout/Container.jsx
import React from 'react';
import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledContainer = styled(Box, {
  shouldForwardProp: (prop) => !['maxWidth', 'padding', 'centered'].includes(prop),
})(({ theme, maxWidth, padding, centered }) => ({
  width: '100%',
  maxWidth: maxWidth || '1280px',
  margin: centered ? '0 auto' : '0',
  padding: padding || theme.spacing(2),

  [theme.breakpoints.up('sm')]: {
    padding: padding || theme.spacing(3),
  },

  [theme.breakpoints.up('lg')]: {
    padding: padding || theme.spacing(4),
  },
}));

/**
 * Container component for consistent layout and spacing
 *
 * @param {Object} props
 * @param {string} props.maxWidth - Maximum width of container
 * @param {string} props.padding - Custom padding override
 * @param {boolean} props.centered - Whether to center the container
 * @param {React.ReactNode} props.children - Child components
 */
const Container = ({
  maxWidth,
  padding,
  centered = true,
  children,
  ...props
}) => {
  return (
    <StyledContainer
      maxWidth={maxWidth}
      padding={padding}
      centered={centered}
      {...props}
    >
      {children}
    </StyledContainer>
  );
};

export default Container;
