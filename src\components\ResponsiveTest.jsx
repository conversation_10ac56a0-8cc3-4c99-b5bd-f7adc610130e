// src/components/ResponsiveTest.jsx
import React from 'react';
import { Box, Paper, Typography, Chip } from '@mui/material';
import { useNavbarResponsive } from '../hooks/useResponsive';

/**
 * Test component to display responsive behavior information
 * This component helps verify that the responsive navbar is working correctly
 */
function ResponsiveTest() {
  const { isMobile, isDesktop, windowWidth, breakpoint } = useNavbarResponsive();

  return (
    <Paper 
      elevation={2} 
      sx={{ 
        p: 3, 
        mb: 3, 
        border: '2px solid',
        borderColor: isMobile ? 'warning.main' : 'success.main',
        backgroundColor: isMobile ? 'warning.light' : 'success.light',
        color: isMobile ? 'warning.contrastText' : 'success.contrastText'
      }}
    >
      <Typography variant="h6" gutterBottom>
        📱 Responsive Navigation Test
      </Typography>
      
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
        <Chip 
          label={`Current Mode: ${isMobile ? 'Mobile' : 'Desktop'}`}
          color={isMobile ? 'warning' : 'success'}
          variant="filled"
        />
        <Chip 
          label={`Window Width: ${windowWidth}px`}
          variant="outlined"
        />
        <Chip 
          label={`Breakpoint: ${breakpoint}px`}
          variant="outlined"
        />
      </Box>

      <Typography variant="body2" sx={{ mb: 1 }}>
        <strong>Expected Behavior:</strong>
      </Typography>
      
      {isMobile ? (
        <Box>
          <Typography variant="body2" color="text.secondary">
            ✅ Mobile Mode (≤768px):
          </Typography>
          <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
            <li>AppBar with hamburger menu should be visible</li>
            <li>Sidebar should be hidden by default</li>
            <li>Clicking hamburger should open overlay drawer</li>
            <li>Main content should have top margin for AppBar</li>
          </ul>
        </Box>
      ) : (
        <Box>
          <Typography variant="body2" color="text.secondary">
            ✅ Desktop Mode (>768px):
          </Typography>
          <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
            <li>No AppBar/hamburger menu should be visible</li>
            <li>Sidebar should be permanently visible</li>
            <li>Main content width should be reduced to accommodate sidebar</li>
            <li>No overlay behavior - sidebar pushes content</li>
          </ul>
        </Box>
      )}

      <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic' }}>
        💡 Resize your browser window to test the responsive behavior!
      </Typography>
    </Paper>
  );
}

export default ResponsiveTest;
