// src/components/design-system/Typography/Heading.jsx
import React from 'react';
import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledHeading = styled(Typography, {
  shouldForwardProp: (prop) => !['size', 'weight'].includes(prop),
})(({ theme, size, weight, color }) => ({
  fontWeight: weight || 'inherit',
  color: color || 'inherit',
  marginBottom: theme.spacing(1),

  // Size variants
  ...(size === 'xs' && {
    fontSize: theme.typography.fontSize?.xs || '0.75rem',
    fontWeight: theme.typography.fontWeight?.semibold || 600,
  }),
  ...(size === 'sm' && {
    fontSize: theme.typography.fontSize?.sm || '0.875rem',
    fontWeight: theme.typography.fontWeight?.semibold || 600,
  }),
  ...(size === 'base' && {
    fontSize: theme.typography.fontSize?.base || '1rem',
    fontWeight: theme.typography.fontWeight?.semibold || 600,
  }),
  ...(size === 'lg' && {
    fontSize: theme.typography.fontSize?.lg || '1.125rem',
    fontWeight: theme.typography.fontWeight?.semibold || 600,
  }),
  ...(size === 'xl' && {
    fontSize: theme.typography.fontSize?.xl || '1.25rem',
    fontWeight: theme.typography.fontWeight?.bold || 700,
  }),
  ...(size === '2xl' && {
    fontSize: theme.typography.fontSize?.[2] || '1.5rem',
    fontWeight: theme.typography.fontWeight?.bold || 700,
  }),
  ...(size === '3xl' && {
    fontSize: theme.typography.fontSize?.[3] || '1.875rem',
    fontWeight: theme.typography.fontWeight?.bold || 700,
  }),
  ...(size === '4xl' && {
    fontSize: theme.typography.fontSize?.[4] || '2.25rem',
    fontWeight: theme.typography.fontWeight?.extrabold || 800,
  }),
}));

/**
 * Heading component for consistent typography hierarchy
 *
 * @param {Object} props
 * @param {'h1'|'h2'|'h3'|'h4'|'h5'|'h6'} props.level - HTML heading level
 * @param {'xs'|'sm'|'base'|'lg'|'xl'|'2xl'|'3xl'|'4xl'} props.size - Visual size (independent of HTML level)
 * @param {number} props.weight - Font weight
 * @param {string} props.color - Text color
 * @param {React.ReactNode} props.children - Text content
 */
const Heading = React.forwardRef(({
  level = 'h2',
  size,
  weight,
  color,
  children,
  ...props
}, ref) => {
  // Map level to default size if size not provided
  const defaultSize = size || {
    h1: '4xl',
    h2: '3xl',
    h3: '2xl',
    h4: 'xl',
    h5: 'lg',
    h6: 'base',
  }[level];

  return (
    <StyledHeading
      ref={ref}
      variant={level}
      component={level}
      size={defaultSize}
      weight={weight}
      color={color}
      {...props}
    >
      {children}
    </StyledHeading>
  );
});

Heading.displayName = 'Heading';

export default Heading;
