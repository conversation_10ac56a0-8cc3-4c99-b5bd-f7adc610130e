// src/pages/ProfileSettings.jsx
import React, { useState, useEffect } from 'react';
import { TextField, Avatar, Grid, Paper, CircularProgress } from '@mui/material';
// Nhost Hooks & GraphQL
import { useUserId, useUserData, useSignOut } from '@nhost/react'; // Import Nhost hooks
import { nhost } from '../services/nhost'; // Import Nhost client for auth methods
import { gql, useMutation } from '@apollo/client'; // Import gql and useMutation

// Design System Components
import {
    Container,
    Stack,
    Heading,
    Text,
    Button,
    Alert
} from '../components/design-system/index';
// Removed Firebase imports

// --- GraphQL Mutation ---
// Update user's displayName (maps to username in our case)
// Ensure 'users' table allows updates to 'displayName' for the 'user' role
const UPDATE_USERNAME_MUTATION = gql`
  mutation UpdateUsername($userId: uuid!, $displayName: String!) {
    updateUser(pk_columns: { id: $userId }, _set: { displayName: $displayName }) {
      id
      displayName
    }
  }
`;

function ProfileSettings() {
  const userId = useUserId();
  const userData = useUserData(); // Nhost hook to get user data (includes email, displayName, etc.)
  const { signOut } = useSignOut();

  const [username, setUsername] = useState('');
  const [profileLoading, setProfileLoading] = useState(false);
  const [actionError, setActionError] = useState(''); // For username update errors
  const [actionSuccess, setActionSuccess] = useState(''); // For success messages

  // Password change state
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordError, setPasswordError] = useState('');

  // Effect to initialize username state when userData loads
  useEffect(() => {
    if (userData) {
      setUsername(userData.displayName || ''); // Use displayName from Nhost user data
    }
  }, [userData]);

  // Define the update username mutation hook
  const [updateUsername, { loading: loadingUpdateUsername }] = useMutation(UPDATE_USERNAME_MUTATION, {
     onError: (err) => {
        console.error("Username update error:", err);
        setActionError(`Failed to update username: ${err.message}`);
        setActionSuccess('');
     },
     onCompleted: (data) => {
         console.log("Username updated:", data?.updateUser?.displayName);
         setActionSuccess('Username updated successfully!');
         setActionError('');
         // Optional: Nhost user data might refetch automatically, or trigger manually if needed
         // nhost.auth.refreshSession(); // Could potentially refresh user data
     }
  });


  // Handle Saving Profile Changes (Username)
  const handleProfileSave = async (e) => {
    e.preventDefault();
    if (!userId || !userData || username.trim() === userData.displayName) {
      return; // No changes or no user
    }
    setActionError('');
    setActionSuccess('');
    // Use the loading state from the mutation hook directly
    updateUsername({
        variables: {
            userId: userId,
            displayName: username.trim()
        }
    });
  };

  // Handle Password Change (using Nhost auth client)
  const handlePasswordChange = async (e) => {
    e.preventDefault();
    if (!userId) return; // Should not happen if page is protected

    setPasswordError('');
    setActionSuccess(''); // Clear other messages

    if (!oldPassword || !newPassword || !confirmPassword) {
      setPasswordError("Please fill in all password fields.");
      return;
    }
    if (newPassword.length < 6) { // Nhost default min length
      setPasswordError("New password must be at least 6 characters long.");
      return;
    }
    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match.");
      return;
    }

    setPasswordLoading(true);

    try {
      // Nhost's changePassword handles re-authentication implicitly if needed
      // based on security keys configuration in Nhost dashboard.
      // It might require the current password if configured that way.
      const { error } = await nhost.auth.changePassword({
        currentPassword: oldPassword, // Provide current password for verification
        newPassword: newPassword,
      });

      if (error) {
        console.error("Password change error:", error);
        // Handle specific Nhost errors
        if (error.message.includes('Invalid current password') || error.message.includes('Incorrect password')) {
             setPasswordError("Incorrect current password.");
        } else if (error.message.includes('Password is too weak') || error.message.includes('Password length')) {
             setPasswordError("The new password is too weak or too short.");
        }
        else {
             setPasswordError(error.message || "Failed to change password.");
        }
      } else {
        setActionSuccess("Password updated successfully!");
        // Clear password fields after success
        setOldPassword('');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch (err) {
      // Catch unexpected errors
      console.error("Unexpected password change error:", err);
      setPasswordError("An unexpected error occurred. Please try again.");
    } finally {
      setPasswordLoading(false);
    }
  };

  // --- Render Component ---
  // Handle loading state for user data
  // Note: useUserData doesn't have an explicit loading state,
  // but userId will be null initially until auth resolves.
  if (!userId) {
    return (
      <Container sx={{ py: 4 }}>
        <Stack justifyContent="center" alignItems="center" sx={{ minHeight: '60vh' }}>
          <CircularProgress />
        </Stack>
      </Container>
    );
  }


  return (
    <Container maxWidth="1024px" sx={{ py: 4 }}>
      <Stack spacing={4}>
        <Heading level="h1" size="4xl">Profile Settings</Heading>

        {/* Display general success/error messages */}
        {actionError && (
          <Alert severity="error" closable onClose={() => setActionError('')}>
            {actionError}
          </Alert>
        )}
        {actionSuccess && (
          <Alert severity="success" closable onClose={() => setActionSuccess('')}>
            {actionSuccess}
          </Alert>
        )}

      <Grid container spacing={4}>
        {/* Account Settings Section */}
        <Grid item xs={12} md={6}>
          <Paper elevation={0} sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 2, border: `1px solid ${theme => theme.palette.divider}` }}>
            <Heading level="h2" size="xl" sx={{ mb: 3 }}>Account</Heading>
            <Stack component="form" onSubmit={handleProfileSave} spacing={2.5}>
              <Avatar sx={{ width: 80, height: 80, mb: 1, alignSelf: 'center' }} alt={username || userData?.email} src={userData?.avatarUrl || undefined}>
                {username ? username[0].toUpperCase() : (userData?.email ? userData.email[0].toUpperCase() : '?')}
              </Avatar>

              <TextField
                label="Username"
                fullWidth
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                inputProps={{ 'aria-label': 'Username' }}
                disabled={loadingUpdateUsername}
              />
              <TextField
                label="Email"
                type="email"
                fullWidth
                value={userData?.email || ''} // Display from Nhost user data
                disabled // Email is generally not changed here
                InputProps={{ readOnly: true }}
                inputProps={{ 'aria-label': 'Email address (read-only)' }}
              />
              <Button
                type="submit"
                variant="contained"
                disabled={loadingUpdateUsername || username.trim() === (userData?.displayName || '')}
                aria-label="Save profile changes"
              >
                {loadingUpdateUsername ? <CircularProgress size={24} color="inherit" /> : 'Save Username'}
              </Button>
            </Stack>
          </Paper>
        </Grid>

        {/* Change Password Section */}
        <Grid item xs={12} md={6}>
          <Paper elevation={0} sx={{ p: 3, bgcolor: 'background.paper', borderRadius: 2, border: `1px solid ${theme => theme.palette.divider}` }}>
            <Heading level="h2" size="xl" sx={{ mb: 2 }}>Change Password</Heading>
            {passwordError && (
              <Alert severity="error" closable onClose={() => setPasswordError('')} sx={{ mb: 2 }}>
                {passwordError}
              </Alert>
            )}
            <Stack component="form" onSubmit={handlePasswordChange} spacing={2.5}>
              <TextField
                label="Current Password"
                type="password"
                fullWidth
                value={oldPassword}
                onChange={(e) => setOldPassword(e.target.value)}
                required
                disabled={passwordLoading}
                inputProps={{ 'aria-label': 'Current password' }}
                autoComplete="current-password"
              />
              <TextField
                label="New Password"
                type="password"
                fullWidth
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                required
                disabled={passwordLoading}
                inputProps={{ 'aria-label': 'New password' }}
                autoComplete="new-password"
              />
              <TextField
                label="Confirm New Password"
                type="password"
                fullWidth
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                disabled={passwordLoading}
                inputProps={{ 'aria-label': 'Confirm new password' }}
                autoComplete="new-password"
                error={newPassword !== confirmPassword && confirmPassword !== ''}
                helperText={newPassword !== confirmPassword && confirmPassword !== '' ? "Passwords do not match" : ""}
              />
              <Button type="submit" variant="contained" disabled={passwordLoading} aria-label="Change password">
                {passwordLoading ? <CircularProgress size={24} color="inherit" /> : 'Change Password'}
              </Button>
            </Stack>
          </Paper>
        </Grid>
      </Grid>
      </Stack>
    </Container>
  );
}

export default ProfileSettings;
