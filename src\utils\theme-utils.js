// src/utils/theme-utils.js
// Theme utility functions for consistent design system usage

import { alpha } from '@mui/material/styles';

/**
 * Get spacing value from theme
 * @param {Object} theme - MUI theme object
 * @param {number|Array} value - Spacing multiplier or array of multipliers
 * @returns {string} - Spacing value in pixels
 */
export const getSpacing = (theme, value) => {
  if (Array.isArray(value)) {
    return value.map(v => theme.spacing(v)).join(' ');
  }
  return theme.spacing(value);
};

/**
 * Get color with opacity
 * @param {string} color - Color value
 * @param {number} opacity - Opacity value (0-1)
 * @returns {string} - Color with alpha
 */
export const getColorWithAlpha = (color, opacity) => {
  return alpha(color, opacity);
};

/**
 * Get responsive value based on breakpoint
 * @param {Object} theme - MUI theme object
 * @param {Object} values - Object with breakpoint keys and values
 * @returns {Object} - Responsive styles object
 */
export const getResponsiveValue = (theme, values) => {
  const styles = {};
  
  Object.entries(values).forEach(([breakpoint, value]) => {
    if (breakpoint === 'xs') {
      styles[breakpoint] = value;
    } else {
      styles[theme.breakpoints.up(breakpoint)] = value;
    }
  });
  
  return styles;
};

/**
 * Get shadow value from design system
 * @param {string} level - Shadow level (xs, sm, base, md, lg, xl, 2xl)
 * @returns {string} - Box shadow value
 */
export const getShadow = (level) => {
  const shadows = {
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    base: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    md: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    lg: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    xl: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  };
  
  return shadows[level] || shadows.base;
};

/**
 * Get border radius value from design system
 * @param {string} size - Border radius size (none, sm, base, md, lg, xl, full)
 * @returns {string} - Border radius value
 */
export const getBorderRadius = (size) => {
  const borderRadius = {
    none: '0',
    sm: '4px',
    base: '8px',
    md: '12px',
    lg: '16px',
    xl: '24px',
    full: '9999px',
  };
  
  return borderRadius[size] || borderRadius.base;
};

/**
 * Get typography values from design system
 * @param {string} size - Typography size (xs, sm, base, lg, xl, 2xl, 3xl, 4xl, 5xl)
 * @returns {Object} - Typography styles object
 */
export const getTypography = (size) => {
  const typography = {
    xs: { fontSize: '0.75rem', lineHeight: 1.5 },
    sm: { fontSize: '0.875rem', lineHeight: 1.5 },
    base: { fontSize: '1rem', lineHeight: 1.6 },
    lg: { fontSize: '1.125rem', lineHeight: 1.6 },
    xl: { fontSize: '1.25rem', lineHeight: 1.6 },
    '2xl': { fontSize: '1.5rem', lineHeight: 1.3 },
    '3xl': { fontSize: '1.875rem', lineHeight: 1.3 },
    '4xl': { fontSize: '2.25rem', lineHeight: 1.2 },
    '5xl': { fontSize: '3rem', lineHeight: 1.2 },
  };
  
  return typography[size] || typography.base;
};

/**
 * Create focus ring styles
 * @param {string} color - Focus ring color
 * @param {number} width - Focus ring width
 * @param {number} offset - Focus ring offset
 * @returns {Object} - Focus ring styles
 */
export const createFocusRing = (color, width = 3, offset = 2) => ({
  outline: 'none',
  boxShadow: `0 0 0 ${width}px ${alpha(color, 0.3)}`,
  outlineOffset: `${offset}px`,
});

/**
 * Get component size styles
 * @param {string} size - Component size (sm, md, lg)
 * @returns {Object} - Size styles object
 */
export const getComponentSize = (size) => {
  const sizes = {
    sm: { height: 32, padding: '6px 12px', fontSize: '0.875rem' },
    md: { height: 40, padding: '8px 16px', fontSize: '1rem' },
    lg: { height: 48, padding: '12px 20px', fontSize: '1.125rem' },
  };
  
  return sizes[size] || sizes.md;
};

/**
 * Create hover transition styles
 * @param {string} property - CSS property to transition
 * @param {string} duration - Transition duration
 * @param {string} easing - Transition easing function
 * @returns {Object} - Transition styles
 */
export const createHoverTransition = (
  property = 'all',
  duration = '0.2s',
  easing = 'cubic-bezier(0.4, 0, 0.2, 1)'
) => ({
  transition: `${property} ${duration} ${easing}`,
});

/**
 * Get semantic color from theme
 * @param {Object} theme - MUI theme object
 * @param {string} semantic - Semantic color name (success, error, warning, info)
 * @param {string} variant - Color variant (main, light, dark)
 * @returns {string} - Color value
 */
export const getSemanticColor = (theme, semantic, variant = 'main') => {
  return theme.palette[semantic]?.[variant] || theme.palette.primary[variant];
};

/**
 * Create gradient background
 * @param {string} direction - Gradient direction
 * @param {Array} colors - Array of color stops
 * @returns {string} - CSS gradient value
 */
export const createGradient = (direction = '45deg', colors = []) => {
  if (colors.length < 2) {
    return 'transparent';
  }
  
  return `linear-gradient(${direction}, ${colors.join(', ')})`;
};

/**
 * Get contrast text color
 * @param {string} backgroundColor - Background color
 * @returns {string} - Contrast text color (black or white)
 */
export const getContrastText = (backgroundColor) => {
  // Simple contrast calculation - in production, use a more sophisticated method
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  
  return brightness > 128 ? '#000000' : '#ffffff';
};
