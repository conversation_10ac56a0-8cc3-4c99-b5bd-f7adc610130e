// src/utils/passUtils.js
// Pass and tier utilities for the unified credit system

/**
 * Pass tier hierarchy for comparison
 */
const PASS_TIER_HIERARCHY = {
  'Free': 1,
  'Paid': 2,
  'Lite': 3,
  'Pro': 4,
  'Elite': 5
};

/**
 * Server access rules by tier and category
 */
const SERVER_ACCESS_RULES = {
  'Visitor': {
    'Free': ['slow'],
    'Paid': ['slow', 'fast'] // Fast until credits exhausted, then auto-downgrade
  },
  'Citizen': {
    'Lite': ['slow', 'fast'],
    'Pro': ['slow', 'fast'],
    'Elite': ['slow', 'fast']
  }
};

/**
 * Get pass tier hierarchy for comparison
 * @returns {Object} Tier hierarchy mapping
 */
export function getPassTierHierarchy() {
  return { ...PASS_TIER_HIERARCHY };
}

/**
 * Check if user can access specific server type
 * @param {string} passTier - User's pass tier
 * @param {string} passCategory - User's pass category
 * @param {string} serverType - Server type to check ('slow' or 'fast')
 * @param {number} creditsBalance - User's current credit balance
 * @returns {Object} Access result
 */
export function canAccessServerType(passTier, passCategory, serverType, creditsBalance = 0) {
  const allowedServers = SERVER_ACCESS_RULES[passCategory]?.[passTier] || [];
  
  if (!allowedServers.includes(serverType)) {
    return {
      allowed: false,
      reason: `${serverType} servers not available for ${passCategory} ${passTier} tier`,
      suggestedUpgrade: getSuggestedUpgrade(passCategory, passTier, serverType)
    };
  }
  
  // For fast servers, check if user has credits (except unlimited tiers)
  if (serverType === 'fast' && !isUnlimitedTier(passTier, passCategory)) {
    if (creditsBalance <= 0) {
      return {
        allowed: false,
        reason: 'No credits available for fast servers',
        needsCredits: true
      };
    }
  }
  
  return { allowed: true };
}

/**
 * Check if tier has unlimited access
 * @param {string} passTier - Pass tier
 * @param {string} passCategory - Pass category
 * @returns {boolean} True if unlimited
 */
export function isUnlimitedTier(passTier, passCategory) {
  return passCategory === 'Citizen' && passTier === 'Elite';
}

/**
 * Check if pass should auto-downgrade when credits exhausted
 * @param {number} creditsBalance - Current credit balance
 * @param {string} passTier - Pass tier
 * @param {string} passCategory - Pass category
 * @returns {boolean} True if should auto-downgrade
 */
export function shouldAutoDowngrade(creditsBalance, passTier, passCategory) {
  // Only Visitor Paid tier auto-downgrades when credits exhausted
  return passCategory === 'Visitor' && 
         passTier === 'Paid' && 
         creditsBalance <= 0;
}

/**
 * Get suggested upgrade for server access
 * @param {string} currentCategory - Current pass category
 * @param {string} currentTier - Current pass tier
 * @param {string} desiredServerType - Desired server type
 * @returns {Object|null} Suggested upgrade or null
 */
export function getSuggestedUpgrade(currentCategory, currentTier, desiredServerType) {
  if (desiredServerType === 'fast') {
    if (currentCategory === 'Visitor' && currentTier === 'Free') {
      return {
        category: 'Visitor',
        tier: 'Paid',
        reason: 'Upgrade to Visitor Paid for fast server access'
      };
    }
    if (currentCategory === 'Visitor') {
      return {
        category: 'Citizen',
        tier: 'Lite',
        reason: 'Upgrade to Citizen for better fast server access'
      };
    }
  }
  return null;
}

/**
 * Get pass features based on tier and category
 * @param {string} passTier - Pass tier
 * @param {string} passCategory - Pass category
 * @returns {Object} Pass features
 */
export function getPassFeatures(passTier, passCategory) {
  const baseFeatures = {
    slowServers: true,
    fastServers: false,
    dailyFreeCredits: 0,
    unlimitedSlow: false,
    prioritySupport: false,
    advancedTools: false,
    autoDowngrade: false
  };
  
  if (passCategory === 'Visitor') {
    if (passTier === 'Free') {
      return {
        ...baseFeatures,
        dailyFreeCredits: 3600,
        fastServers: false
      };
    }
    if (passTier === 'Paid') {
      return {
        ...baseFeatures,
        fastServers: true,
        autoDowngrade: true
      };
    }
  }
  
  if (passCategory === 'Citizen') {
    const citizenBase = {
      ...baseFeatures,
      fastServers: true,
      unlimitedSlow: true
    };
    
    switch (passTier) {
      case 'Lite':
        return citizenBase;
      case 'Pro':
        return {
          ...citizenBase,
          prioritySupport: true,
          advancedTools: true
        };
      case 'Elite':
        return {
          ...citizenBase,
          prioritySupport: true,
          advancedTools: true,
          unlimitedFast: true
        };
    }
  }
  
  return baseFeatures;
}

/**
 * Compare two pass tiers
 * @param {string} tier1 - First tier
 * @param {string} tier2 - Second tier
 * @returns {number} -1 if tier1 < tier2, 0 if equal, 1 if tier1 > tier2
 */
export function comparePassTiers(tier1, tier2) {
  const rank1 = PASS_TIER_HIERARCHY[tier1] || 0;
  const rank2 = PASS_TIER_HIERARCHY[tier2] || 0;
  
  if (rank1 < rank2) return -1;
  if (rank1 > rank2) return 1;
  return 0;
}

/**
 * Get available server types for user
 * @param {string} passTier - User's pass tier
 * @param {string} passCategory - User's pass category
 * @param {number} creditsBalance - User's credit balance
 * @returns {Array} Available server types
 */
export function getAvailableServerTypes(passTier, passCategory, creditsBalance = 0) {
  const allowedServers = SERVER_ACCESS_RULES[passCategory]?.[passTier] || [];
  
  return allowedServers.filter(serverType => {
    const access = canAccessServerType(passTier, passCategory, serverType, creditsBalance);
    return access.allowed;
  });
}

/**
 * Get server cost information
 * @param {string} serverType - Server type
 * @returns {Object} Cost information
 */
export function getServerCostInfo(serverType) {
  return {
    serverType,
    creditsPerSecond: 1, // Unified system: 1 credit = 1 second
    displayName: serverType === 'fast' ? 'Fast Server' : 'Slow Server',
    description: serverType === 'fast' ? 
      'High-performance processing with faster results' : 
      'Standard processing with reliable results'
  };
}

/**
 * Validate pass upgrade
 * @param {Object} currentPass - Current pass object
 * @param {Object} targetPass - Target pass object
 * @returns {Object} Validation result
 */
export function validatePassUpgrade(currentPass, targetPass) {
  if (!currentPass || !targetPass) {
    return {
      valid: false,
      error: 'Invalid pass data'
    };
  }
  
  const currentRank = PASS_TIER_HIERARCHY[currentPass.tier] || 0;
  const targetRank = PASS_TIER_HIERARCHY[targetPass.tier] || 0;
  
  if (targetRank <= currentRank && currentPass.category === targetPass.category) {
    return {
      valid: false,
      error: 'Target pass is not an upgrade'
    };
  }
  
  return { valid: true };
}
