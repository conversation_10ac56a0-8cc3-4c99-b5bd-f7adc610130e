// jest.config.mjs
export default {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node', 'mjs'],
  
  transform: {
    '^.+\\.(ts|tsx)$': [
      'ts-jest',
      {
        useESM: true,
        tsconfig: 'tsconfig.json'
      }
    ],
    '^.+\\.(js|jsx)$': [
      'babel-jest',
      {
        presets: [
          ['@babel/preset-env', { targets: { node: 'current' } }],
          '@babel/preset-typescript'
        ],
        plugins: ['@babel/plugin-transform-modules-commonjs']
      }
    ]
  },
  
  transformIgnorePatterns: [
    'node_modules/(?!(@nhost|uuid|graphql-request|cross-fetch)/)'
  ],
  
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^(\\.{1,2}/.*)\\.js$': '$1'
  },
  
  // Define paths for test discovery
  roots: ['<rootDir>/functions'],
  testMatch: ['**/__tests__/**/*.test.(ts|js|mjs)'],
  
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  
  verbose: true,
  
  setupFilesAfterEnv: [
    '<rootDir>/functions/credit/__tests__/setup.ts'
  ],
  
  // Collect coverage data
  collectCoverage: true,
  collectCoverageFrom: [
    'functions/**/*.{ts,tsx,js,jsx}',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/__tests__/**'
  ],
  
  moduleDirectories: ['node_modules', '<rootDir>'],
  
  // Test environments
  testEnvironmentOptions: {
    url: 'http://localhost/'
  }
};

