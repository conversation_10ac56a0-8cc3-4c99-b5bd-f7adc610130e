// src/theme.js
import { createTheme, alpha } from '@mui/material/styles';
import { grey, blueGrey, green, red, orange, blue } from '@mui/material/colors';

// Design System Constants
const SPACING_UNIT = 4; // Base spacing unit in pixels

// Spacing scale (4px base unit)
const spacing = {
  xs: SPACING_UNIT, // 4px
  sm: SPACING_UNIT * 2, // 8px
  md: SPACING_UNIT * 4, // 16px
  lg: SPACING_UNIT * 6, // 24px
  xl: SPACING_UNIT * 8, // 32px
  '2xl': SPACING_UNIT * 12, // 48px
  '3xl': SPACING_UNIT * 16, // 64px
};

// Border radius scale
const borderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

// Typography scale
const typography = {
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
  },
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
  },
  lineHeight: {
    tight: 1.2,
    snug: 1.3,
    normal: 1.5,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// Shadow/elevation scale
const shadows = {
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  base: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  md: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  lg: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  xl: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
};

// Component sizing scale
const componentSizes = {
  sm: { height: 32, padding: '6px 12px', fontSize: typography.fontSize.sm },
  md: { height: 40, padding: '8px 16px', fontSize: typography.fontSize.base },
  lg: { height: 48, padding: '12px 20px', fontSize: typography.fontSize.lg },
};

// Helper for focus ring shadow
const createFocusRing = (color) => `0 0 0 3px ${alpha(color, 0.3)}`;

const getDesignTokens = (mode) => ({
  palette: {
    mode,
    // Primary brand color (Green)
    primary: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: green[500], // Main brand color
      600: green[600],
      700: green[700],
      800: green[800],
      900: green[900],
      main: green[600],
      light: green[400],
      dark: green[800],
      contrastText: '#ffffff',
    },
    // Secondary color (Blue Grey)
    secondary: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: blueGrey[500],
      600: blueGrey[600],
      700: blueGrey[700],
      800: blueGrey[800],
      900: blueGrey[900],
      main: blueGrey[500],
      light: blueGrey[400],
      dark: blueGrey[700],
      contrastText: '#ffffff',
    },
    // Semantic colors
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: red[500],
      600: red[600],
      700: red[700],
      800: red[800],
      900: red[900],
      main: red[600],
      light: red[400],
      dark: red[800],
      contrastText: '#ffffff',
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: orange[500],
      600: orange[600],
      700: orange[700],
      800: orange[800],
      900: orange[900],
      main: orange[600],
      light: orange[400],
      dark: orange[800],
      contrastText: '#ffffff',
    },
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: blue[500],
      600: blue[600],
      700: blue[700],
      800: blue[800],
      900: blue[900],
      main: blue[600],
      light: blue[400],
      dark: blue[800],
      contrastText: '#ffffff',
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: green[500],
      600: green[600],
      700: green[700],
      800: green[800],
      900: green[900],
      main: green[600],
      light: green[400],
      dark: green[800],
      contrastText: '#ffffff',
    },
    // Neutral grays
    grey: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
      ...grey,
    },
    // Background colors
    background: {
      default: mode === 'light' ? '#fafafa' : '#0a0a0a',
      paper: mode === 'light' ? '#ffffff' : '#171717',
      surface: mode === 'light' ? '#f5f5f5' : '#262626',
    },
    // Text colors
    text: {
      primary: mode === 'light' ? '#171717' : '#fafafa',
      secondary: mode === 'light' ? '#525252' : '#a3a3a3',
      disabled: mode === 'light' ? '#a3a3a3' : '#525252',
    },
    // Divider color
    divider: mode === 'light' ? '#e5e5e5' : '#404040',
    // Action states
    action: {
      hover: mode === 'light' ? alpha('#000000', 0.04) : alpha('#ffffff', 0.08),
      selected: mode === 'light' ? alpha(green[500], 0.08) : alpha(green[500], 0.12),
      disabled: mode === 'light' ? alpha('#000000', 0.26) : alpha('#ffffff', 0.3),
      disabledBackground: mode === 'light' ? alpha('#000000', 0.12) : alpha('#ffffff', 0.12),
      focus: mode === 'light' ? alpha(green[500], 0.12) : alpha(green[500], 0.16),
    },
  },
  // Shape configuration
  shape: {
    borderRadius: borderRadius.base,
  },
  // Spacing configuration
  spacing: (factor) => `${SPACING_UNIT * factor}px`,
  // Typography configuration
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    // Heading styles
    h1: {
      fontWeight: typography.fontWeight.extrabold,
      fontSize: typography.fontSize['4xl'],
      lineHeight: typography.lineHeight.tight,
      letterSpacing: '-0.025em',
    },
    h2: {
      fontWeight: typography.fontWeight.bold,
      fontSize: typography.fontSize['3xl'],
      lineHeight: typography.lineHeight.tight,
      letterSpacing: '-0.025em',
    },
    h3: {
      fontWeight: typography.fontWeight.semibold,
      fontSize: typography.fontSize['2xl'],
      lineHeight: typography.lineHeight.snug,
      letterSpacing: '-0.015em',
    },
    h4: {
      fontWeight: typography.fontWeight.semibold,
      fontSize: typography.fontSize.xl,
      lineHeight: typography.lineHeight.snug,
    },
    h5: {
      fontWeight: typography.fontWeight.semibold,
      fontSize: typography.fontSize.lg,
      lineHeight: typography.lineHeight.normal,
    },
    h6: {
      fontWeight: typography.fontWeight.semibold,
      fontSize: typography.fontSize.base,
      lineHeight: typography.lineHeight.normal,
    },
    // Body text styles
    body1: {
      fontWeight: typography.fontWeight.normal,
      fontSize: typography.fontSize.base,
      lineHeight: typography.lineHeight.relaxed,
    },
    body2: {
      fontWeight: typography.fontWeight.normal,
      fontSize: typography.fontSize.sm,
      lineHeight: typography.lineHeight.normal,
    },
    // Utility text styles
    subtitle1: {
      fontWeight: typography.fontWeight.medium,
      fontSize: typography.fontSize.base,
      lineHeight: typography.lineHeight.normal,
    },
    subtitle2: {
      fontWeight: typography.fontWeight.medium,
      fontSize: typography.fontSize.sm,
      lineHeight: typography.lineHeight.normal,
    },
    caption: {
      fontWeight: typography.fontWeight.normal,
      fontSize: typography.fontSize.xs,
      lineHeight: typography.lineHeight.normal,
    },
    overline: {
      fontWeight: typography.fontWeight.medium,
      fontSize: typography.fontSize.xs,
      lineHeight: typography.lineHeight.normal,
      textTransform: 'uppercase',
      letterSpacing: '0.1em',
    },
    button: {
      fontWeight: typography.fontWeight.medium,
      fontSize: typography.fontSize.sm,
      lineHeight: typography.lineHeight.normal,
      textTransform: 'none',
      letterSpacing: '0.01em',
    },
  },
  // Breakpoints
  breakpoints: {
    values: {
      xs: 0,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      '2xl': 1536,
    },
  },
  // Shadows
  shadows: [
    'none',
    shadows.xs,
    shadows.sm,
    shadows.base,
    shadows.md,
    shadows.lg,
    shadows.xl,
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
    shadows['2xl'],
  ],
  // Component overrides
  components: {
    // Button component
    MuiButton: {
      defaultProps: {
        disableElevation: true,
        disableRipple: false,
      },
      styleOverrides: {
        root: ({ theme, ownerState }) => ({
          borderRadius: borderRadius.md,
          fontWeight: typography.fontWeight.medium,
          textTransform: 'none',
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          border: '1px solid transparent',
          position: 'relative',
          overflow: 'hidden',

          // Size variants
          ...(ownerState.size === 'small' && {
            height: componentSizes.sm.height,
            padding: componentSizes.sm.padding,
            fontSize: componentSizes.sm.fontSize,
          }),
          ...(ownerState.size === 'medium' && {
            height: componentSizes.md.height,
            padding: componentSizes.md.padding,
            fontSize: componentSizes.md.fontSize,
          }),
          ...(ownerState.size === 'large' && {
            height: componentSizes.lg.height,
            padding: componentSizes.lg.padding,
            fontSize: componentSizes.lg.fontSize,
          }),

          // Primary contained variant
          ...(ownerState.variant === 'contained' && ownerState.color === 'primary' && {
            backgroundColor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText,
            boxShadow: shadows.sm,
            '&::before': {
              content: '""',
              position: 'absolute',
              inset: '-2px',
              zIndex: -1,
              background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff)',
              borderRadius: 'inherit',
              filter: 'blur(8px)',
              opacity: 0,
              transition: 'opacity 0.3s ease',
            },
            '&:hover': {
              backgroundColor: theme.palette.primary.dark,
              transform: 'translateY(-1px)',
              boxShadow: shadows.md,
              '&::before': {
                opacity: theme.palette.mode === 'dark' ? 0.6 : 0.4,
              },
            },
            '&:active': {
              transform: 'translateY(0)',
              boxShadow: shadows.sm,
            },
            '&:focus-visible': {
              outline: 'none',
              boxShadow: createFocusRing(theme.palette.primary.main),
            },
          }),

          // Secondary contained variant
          ...(ownerState.variant === 'contained' && ownerState.color === 'secondary' && {
            backgroundColor: theme.palette.secondary.main,
            color: theme.palette.secondary.contrastText,
            boxShadow: shadows.sm,
            '&:hover': {
              backgroundColor: theme.palette.secondary.dark,
              transform: 'translateY(-1px)',
              boxShadow: shadows.md,
            },
            '&:active': {
              transform: 'translateY(0)',
              boxShadow: shadows.sm,
            },
            '&:focus-visible': {
              outline: 'none',
              boxShadow: createFocusRing(theme.palette.secondary.main),
            },
          }),

          // Outlined variant
          ...(ownerState.variant === 'outlined' && {
            borderColor: theme.palette.divider,
            color: theme.palette.text.primary,
            backgroundColor: 'transparent',
            '&:hover': {
              borderColor: theme.palette.primary.main,
              backgroundColor: theme.palette.action.hover,
              transform: 'translateY(-1px)',
            },
            '&:focus-visible': {
              outline: 'none',
              boxShadow: createFocusRing(theme.palette.primary.main),
            },
          }),

          // Text variant
          ...(ownerState.variant === 'text' && {
            color: theme.palette.text.primary,
            backgroundColor: 'transparent',
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
            '&:focus-visible': {
              outline: 'none',
              backgroundColor: theme.palette.action.focus,
              boxShadow: createFocusRing(theme.palette.primary.main),
            },
          }),

          // Disabled state
          '&:disabled': {
            opacity: 0.6,
            cursor: 'not-allowed',
            transform: 'none',
            '&::before': {
              display: 'none',
            },
          },
        }),
      },
      variants: [
        {
          props: { variant: 'ghost' },
          style: ({ theme }) => ({
            backgroundColor: 'transparent',
            color: theme.palette.text.secondary,
            border: 'none',
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
              color: theme.palette.text.primary,
            },
            '&:focus-visible': {
              outline: 'none',
              backgroundColor: theme.palette.action.focus,
              boxShadow: createFocusRing(theme.palette.primary.main),
            },
          }),
        },
      ],
    },
    // Card component
    MuiCard: {
      defaultProps: {
        elevation: 0,
      },
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: borderRadius.lg,
          border: `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.background.paper,
          boxShadow: shadows.sm,
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          overflow: 'hidden',
          position: 'relative',
          '&:hover': {
            borderColor: theme.palette.primary.main,
            boxShadow: shadows.md,
            transform: 'translateY(-2px)',
          },
        }),
      },
    },

    // CardActionArea component
    MuiCardActionArea: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: 'inherit',
          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:focus-visible': {
            outline: 'none',
            boxShadow: createFocusRing(theme.palette.primary.main),
          },
          '&:hover .MuiCardActionArea-focusHighlight': {
            opacity: 0,
          },
        }),
      },
    },

    // TextField component
    MuiTextField: {
      defaultProps: {
        variant: 'outlined',
        size: 'medium',
      },
      styleOverrides: {
        root: ({ theme }) => ({
          '& .MuiOutlinedInput-root': {
            borderRadius: borderRadius.md,
            backgroundColor: theme.palette.background.paper,
            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
            '& fieldset': {
              borderColor: theme.palette.divider,
              borderWidth: '1px',
            },
            '&:hover:not(.Mui-disabled) fieldset': {
              borderColor: theme.palette.text.secondary,
            },
            '&.Mui-focused fieldset': {
              borderColor: theme.palette.primary.main,
              borderWidth: '2px',
            },
            '&.Mui-error fieldset': {
              borderColor: theme.palette.error.main,
            },
            '&.Mui-disabled': {
              backgroundColor: theme.palette.action.disabledBackground,
              '& fieldset': {
                borderColor: theme.palette.action.disabled,
              },
            },
          },
          '& .MuiInputLabel-root': {
            color: theme.palette.text.secondary,
            '&.Mui-focused': {
              color: theme.palette.primary.main,
            },
            '&.Mui-error': {
              color: theme.palette.error.main,
            },
          },
        }),
      },
    },

    // Paper component
    MuiPaper: {
      defaultProps: {
        elevation: 0,
      },
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: theme.palette.background.paper,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: borderRadius.lg,
          boxShadow: shadows.sm,
        }),
      },
    },

    // Alert component
    MuiAlert: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: borderRadius.md,
          border: `1px solid`,
          '&.MuiAlert-standardSuccess': {
            backgroundColor: alpha(theme.palette.success.main, 0.1),
            borderColor: theme.palette.success.main,
            color: theme.palette.success.dark,
          },
          '&.MuiAlert-standardError': {
            backgroundColor: alpha(theme.palette.error.main, 0.1),
            borderColor: theme.palette.error.main,
            color: theme.palette.error.dark,
          },
          '&.MuiAlert-standardWarning': {
            backgroundColor: alpha(theme.palette.warning.main, 0.1),
            borderColor: theme.palette.warning.main,
            color: theme.palette.warning.dark,
          },
          '&.MuiAlert-standardInfo': {
            backgroundColor: alpha(theme.palette.info.main, 0.1),
            borderColor: theme.palette.info.main,
            color: theme.palette.info.dark,
          },
        }),
      },
    },

    // Avatar component
    MuiAvatar: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: borderRadius.md,
        }),
        rounded: ({ theme }) => ({
          borderRadius: borderRadius.full,
        }),
      },
    },

    // Chip component
    MuiChip: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: borderRadius.full,
          fontWeight: typography.fontWeight.medium,
        }),
      },
    },

    // AppBar component
    MuiAppBar: {
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: theme.palette.background.paper,
          color: theme.palette.text.primary,
          borderBottom: `1px solid ${theme.palette.divider}`,
          boxShadow: 'none',
        }),
      },
    },

    // Drawer component
    MuiDrawer: {
      styleOverrides: {
        paper: ({ theme }) => ({
          backgroundColor: theme.palette.background.paper,
          borderRight: `1px solid ${theme.palette.divider}`,
        }),
      },
    },

    // List components
    MuiListItemButton: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: borderRadius.md,
          margin: '2px 8px',
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
          },
          '&.Mui-selected': {
            backgroundColor: theme.palette.action.selected,
            '&:hover': {
              backgroundColor: theme.palette.action.selected,
            },
          },
          '&:focus-visible': {
            outline: 'none',
            boxShadow: createFocusRing(theme.palette.primary.main),
          },
        }),
      },
    },

    // Table components
    MuiTableContainer: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderRadius: borderRadius.lg,
          border: `1px solid ${theme.palette.divider}`,
          overflow: 'hidden',
        }),
      },
    },

    MuiTableHead: {
      styleOverrides: {
        root: ({ theme }) => ({
          backgroundColor: theme.palette.background.surface,
        }),
      },
    },

    MuiTableCell: {
      styleOverrides: {
        root: ({ theme }) => ({
          borderBottom: `1px solid ${theme.palette.divider}`,
        }),
        head: ({ theme }) => ({
          fontWeight: typography.fontWeight.semibold,
          color: theme.palette.text.primary,
        }),
      },
    },
  },
});

// Export design tokens for use in components
export const designTokens = {
  spacing,
  borderRadius,
  typography,
  shadows,
  componentSizes,
};

export default getDesignTokens;