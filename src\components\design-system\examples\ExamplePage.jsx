// src/components/design-system/examples/ExamplePage.jsx
// Example page demonstrating design system usage

import React, { useState } from 'react';
import { 
  Container, 
  Grid, 
  Stack, 
  Divider,
  Heading, 
  Text, 
  Label, 
  Caption,
  Button, 
  Input, 
  Card,
  Alert, 
  Badge, 
  Loading 
} from '../index';
import { Box, IconButton } from '@mui/material';
import { 
  Settings as SettingsIcon, 
  Notifications as NotificationsIcon,
  Star as StarIcon 
} from '@mui/icons-material';

const ExamplePage = () => {
  const [inputValue, setInputValue] = useState('');
  const [showAlert, setShowAlert] = useState(true);
  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setShowAlert(true);
    }, 2000);
  };

  return (
    <Container maxWidth="1200px">
      <Stack spacing={6}>
        {/* Header Section */}
        <Box>
          <Heading level="h1" size="4xl">
            Design System Examples
          </Heading>
          <Text size="lg" variant="muted">
            Comprehensive examples of all design system components
          </Text>
        </Box>

        <Divider />

        {/* Typography Section */}
        <Box>
          <Heading level="h2" size="3xl">Typography</Heading>
          <Stack spacing={3}>
            <Box>
              <Heading level="h3" size="2xl">Headings</Heading>
              <Stack spacing={2}>
                <Heading level="h1" size="4xl">Heading 1 - 4xl</Heading>
                <Heading level="h2" size="3xl">Heading 2 - 3xl</Heading>
                <Heading level="h3" size="2xl">Heading 3 - 2xl</Heading>
                <Heading level="h4" size="xl">Heading 4 - xl</Heading>
                <Heading level="h5" size="lg">Heading 5 - lg</Heading>
                <Heading level="h6" size="base">Heading 6 - base</Heading>
              </Stack>
            </Box>

            <Box>
              <Heading level="h3" size="xl">Text Variants</Heading>
              <Stack spacing={1}>
                <Text size="xl">Extra large text</Text>
                <Text size="lg">Large text</Text>
                <Text size="base">Base text (default)</Text>
                <Text size="sm">Small text</Text>
                <Text size="xs">Extra small text</Text>
                <Text variant="muted">Muted text</Text>
                <Text variant="subtle">Subtle text</Text>
                <Caption>Caption text</Caption>
              </Stack>
            </Box>
          </Stack>
        </Box>

        <Divider />

        {/* Buttons Section */}
        <Box>
          <Heading level="h2" size="3xl">Buttons</Heading>
          <Grid.Container gap={3}>
            <Grid.Item xs={12} md={6}>
              <Heading level="h3" size="xl">Button Variants</Heading>
              <Stack spacing={2}>
                <Button variant="contained" color="primary">
                  Primary Button
                </Button>
                <Button variant="contained" color="secondary">
                  Secondary Button
                </Button>
                <Button variant="outlined">
                  Outlined Button
                </Button>
                <Button variant="text">
                  Text Button
                </Button>
                <Button variant="ghost">
                  Ghost Button
                </Button>
              </Stack>
            </Grid.Item>

            <Grid.Item xs={12} md={6}>
              <Heading level="h3" size="xl">Button Sizes & States</Heading>
              <Stack spacing={2}>
                <Button size="large">Large Button</Button>
                <Button size="medium">Medium Button</Button>
                <Button size="small">Small Button</Button>
                <Button disabled>Disabled Button</Button>
                <Button loading={loading} onClick={handleSubmit}>
                  {loading ? 'Loading...' : 'Submit'}
                </Button>
              </Stack>
            </Grid.Item>
          </Grid.Container>

          <Box sx={{ mt: 3 }}>
            <Heading level="h3" size="xl">Icon Buttons</Heading>
            <Stack.Horizontal spacing={2}>
              <Button.Icon ariaLabel="Settings" size="small">
                <SettingsIcon />
              </Button.Icon>
              <Button.Icon ariaLabel="Settings" size="medium">
                <SettingsIcon />
              </Button.Icon>
              <Button.Icon ariaLabel="Settings" size="large">
                <SettingsIcon />
              </Button.Icon>
            </Stack.Horizontal>
          </Box>
        </Box>

        <Divider />

        {/* Form Elements */}
        <Box>
          <Heading level="h2" size="3xl">Form Elements</Heading>
          <Grid.Container gap={3}>
            <Grid.Item xs={12} md={6}>
              <Stack spacing={3}>
                <Input
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  required
                  helperText="We'll never share your email"
                />
                
                <Input
                  label="Password"
                  type="password"
                  placeholder="Enter your password"
                  required
                />

                <Input
                  label="Disabled Input"
                  placeholder="This is disabled"
                  disabled
                />

                <Input
                  label="Error Input"
                  placeholder="This has an error"
                  error
                  helperText="This field is required"
                />
              </Stack>
            </Grid.Item>

            <Grid.Item xs={12} md={6}>
              <Stack spacing={3}>
                <Box>
                  <Label required>Custom Label</Label>
                  <Caption>This is a caption explaining the field</Caption>
                </Box>
                
                <Input
                  label="Small Input"
                  size="small"
                  placeholder="Small size input"
                />
              </Stack>
            </Grid.Item>
          </Grid.Container>
        </Box>

        <Divider />

        {/* Cards Section */}
        <Box>
          <Heading level="h2" size="3xl">Cards</Heading>
          <Grid.Container gap={3}>
            <Grid.Item xs={12} md={4}>
              <Card variant="default">
                <Card.Header 
                  title="Default Card" 
                  subheader="Basic card example"
                />
                <Card.Content>
                  <Text>
                    This is a default card with standard styling and subtle shadows.
                  </Text>
                </Card.Content>
                <Card.Actions>
                  <Button size="small">Learn More</Button>
                </Card.Actions>
              </Card>
            </Grid.Item>

            <Grid.Item xs={12} md={4}>
              <Card variant="outlined">
                <Card.Header 
                  title="Outlined Card" 
                  subheader="Card with border"
                />
                <Card.Content>
                  <Text>
                    This card uses an outlined variant with a visible border.
                  </Text>
                </Card.Content>
                <Card.Actions>
                  <Button size="small" variant="outlined">Action</Button>
                </Card.Actions>
              </Card>
            </Grid.Item>

            <Grid.Item xs={12} md={4}>
              <Card variant="elevated" interactive>
                <Card.Header 
                  title="Interactive Card" 
                  subheader="Hover to see effect"
                />
                <Card.Content>
                  <Text>
                    This card is interactive and responds to hover with animations.
                  </Text>
                </Card.Content>
                <Card.Actions>
                  <Button size="small" variant="contained">
                    Click Me
                  </Button>
                </Card.Actions>
              </Card>
            </Grid.Item>
          </Grid.Container>
        </Box>

        <Divider />

        {/* Feedback Components */}
        <Box>
          <Heading level="h2" size="3xl">Feedback Components</Heading>
          
          <Box sx={{ mb: 3 }}>
            <Heading level="h3" size="xl">Alerts</Heading>
            <Stack spacing={2}>
              <Alert severity="success" closable open={showAlert} onClose={() => setShowAlert(false)}>
                Success! Your action was completed successfully.
              </Alert>
              <Alert severity="info" title="Information">
                This is an informational message with a title.
              </Alert>
              <Alert severity="warning">
                Warning: Please review your input before proceeding.
              </Alert>
              <Alert severity="error">
                Error: Something went wrong. Please try again.
              </Alert>
            </Stack>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Heading level="h3" size="xl">Badges</Heading>
            <Stack.Horizontal spacing={2} alignItems="center">
              <Badge color="success">Active</Badge>
              <Badge color="error">Inactive</Badge>
              <Badge color="warning">Pending</Badge>
              <Badge color="info">Draft</Badge>
              <Badge.Notification count={5}>
                <IconButton>
                  <NotificationsIcon />
                </IconButton>
              </Badge.Notification>
            </Stack.Horizontal>
          </Box>

          <Box>
            <Heading level="h3" size="xl">Loading States</Heading>
            <Grid.Container gap={3}>
              <Grid.Item xs={12} md={4}>
                <Loading type="spinner" size="medium" centered />
              </Grid.Item>
              <Grid.Item xs={12} md={4}>
                <Loading type="linear" text="Loading content..." />
              </Grid.Item>
              <Grid.Item xs={12} md={4}>
                <Loading.Skeleton lines={3} avatar />
              </Grid.Item>
            </Grid.Container>
          </Box>
        </Box>
      </Stack>
    </Container>
  );
};

export default ExamplePage;
