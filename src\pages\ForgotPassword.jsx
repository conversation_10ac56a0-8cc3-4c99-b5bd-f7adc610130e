// src/pages/ForgotPassword.jsx
import React, { useState } from 'react';
import {
  Container, Typo<PERSON>, TextField, Button, Box, Alert, Link as MuiLink,
  CircularProgress, InputAdornment, Paper
} from '@mui/material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import EmailIcon from '@mui/icons-material/Email';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { resetPassword } from '../services/auth';
import { validateEmail } from '../utils/validation';

function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [touched, setTouched] = useState(false);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  // Handle email change with validation
  const handleEmailChange = (e) => {
    const value = e.target.value;
    setEmail(value);
    
    if (touched) {
      const validation = validateEmail(value);
      setEmailError(validation.isValid ? '' : validation.message);
    }
  };

  // Handle email blur
  const handleEmailBlur = () => {
    setTouched(true);
    const validation = validateEmail(email);
    setEmailError(validation.isValid ? '' : validation.message);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    // Validate email
    const validation = validateEmail(email);
    setEmailError(validation.isValid ? '' : validation.message);
    setTouched(true);
    
    if (!validation.isValid) {
      setError('Please enter a valid email address.');
      return;
    }

    setLoading(true);

    try {
      await resetPassword(email.trim());
      
      setSuccess(true);
      enqueueSnackbar('Password reset email sent! Check your inbox.', { 
        variant: 'success',
        autoHideDuration: 5000
      });
    } catch (err) {
      console.error("Password reset failed:", err);
      const errorMessage = err.message || 'Failed to send password reset email. Please try again.';
      setError(errorMessage);
      enqueueSnackbar(errorMessage, { 
        variant: 'error',
        autoHideDuration: 6000
      });
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Container component="main" maxWidth="xs" sx={{ mt: { xs: 4, sm: 8 }, mb: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2, border: theme => `1px solid ${theme.palette.divider}`, boxShadow: 1 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <CheckCircleIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
          
          <Typography component="h1" variant="h4" align="center" gutterBottom sx={{ mb: 2 }}>
            Check Your Email
          </Typography>
          
          <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 3 }}>
            We've sent a password reset link to:
            <br />
            <Box component="strong" sx={{ fontWeight: 'medium', my: 1, display: 'inline-block', wordBreak: 'break-all' }}>
              {email}
            </Box>
            <br />
            Click the link in the email to reset your password. Check your spam folder if you don't see it.
          </Typography>

          <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1.5 }}>
            <Button
              variant="contained"
              fullWidth
              onClick={() => setSuccess(false)}
              sx={{ py: 1.2 }}
            >
              Send Another Email
            </Button>
            
            <Button
              variant="outlined"
              fullWidth
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/login')}
              sx={{ py: 1.2 }}
            >
              Back to Login
            </Button>
          </Box>
        </Box>
      </Container>
    );
  }

  return (
    <Container component="main" maxWidth="xs" sx={{ mt: { xs: 4, sm: 8 }, mb: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2, border: theme => `1px solid ${theme.palette.divider}`, boxShadow: 1 }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography component="h1" variant="h4" align="center" gutterBottom sx={{ mb: 2 }}>
          Reset Password
        </Typography>
        
        <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 3 }}>
          Enter your email address and we'll send you a link to reset your password.
        </Typography>

        {/* Form-level error alert */}
        {error && (
          <Alert 
            severity="error" 
            sx={{ width: '100%', mb: 2 }} 
            onClose={() => setError('')}
            icon={<ErrorOutlineIcon />}
          >
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit} noValidate sx={{ width: '100%' }}>
          {/* Email Field */}
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email Address"
            name="email"
            type="email"
            autoComplete="email"
            autoFocus
            value={email}
            onChange={handleEmailChange}
            onBlur={handleEmailBlur}
            disabled={loading}
            error={touched && Boolean(emailError)}
            helperText={touched && emailError}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon color="action" />
                  </InputAdornment>
                ),
              },
            }}
            aria-label="Email address"
          />

          {/* Reset Button */}
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2, py: 1.5 }}
            disabled={loading}
            aria-label="Send reset email button"
          >
            {loading ? <CircularProgress size={24} color="inherit" /> : 'Send Reset Email'}
          </Button>

          {/* Back to Login Link */}
          <Typography variant="body2" align="center">
            Remember your password?{' '}
            <MuiLink component={RouterLink} to="/login" variant="body2">
              Back to Login
            </MuiLink>
          </Typography>
        </Box>
      </Box>
    </Container>
  );
}

export default ForgotPassword;
