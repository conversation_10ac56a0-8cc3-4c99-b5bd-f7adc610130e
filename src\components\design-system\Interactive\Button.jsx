// src/components/design-system/Interactive/Button.jsx
import React from 'react';
import { Button as Mu<PERSON>Butt<PERSON>, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledButton = styled(MuiButton)(({ fullWidth }) => ({
  ...(fullWidth && {
    width: '100%',
  }),
}));

const StyledIconButton = styled(IconButton)(({ size }) => ({
  // Size variants for icon buttons
  ...(size === 'small' && {
    width: 32,
    height: 32,
  }),
  ...(size === 'medium' && {
    width: 40,
    height: 40,
  }),
  ...(size === 'large' && {
    width: 48,
    height: 48,
  }),
}));

/**
 * Button component with consistent styling and variants
 *
 * @param {Object} props
 * @param {'contained'|'outlined'|'text'|'ghost'} props.variant - Button variant
 * @param {'primary'|'secondary'|'error'|'warning'|'info'|'success'} props.color - Button color
 * @param {'small'|'medium'|'large'} props.size - Button size
 * @param {boolean} props.fullWidth - Whether button takes full width
 * @param {boolean} props.disabled - Whether button is disabled
 * @param {boolean} props.loading - Whether button shows loading state
 * @param {React.ReactNode} props.startIcon - Icon before text
 * @param {React.ReactNode} props.endIcon - Icon after text
 * @param {function} props.onClick - Click handler
 * @param {React.ReactNode} props.children - Button text
 */
const Button = ({
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  fullWidth = false,
  disabled = false,
  loading = false,
  startIcon,
  endIcon,
  onClick,
  children,
  ...props
}) => {
  return (
    <StyledButton
      variant={variant}
      color={color}
      size={size}
      fullWidth={fullWidth}
      disabled={disabled || loading}
      startIcon={loading ? null : startIcon}
      endIcon={loading ? null : endIcon}
      onClick={onClick}
      {...props}
    >
      {loading ? 'Loading...' : children}
    </StyledButton>
  );
};

/**
 * IconButton component for icon-only buttons
 *
 * @param {Object} props
 * @param {'small'|'medium'|'large'} props.size - Button size
 * @param {'primary'|'secondary'|'error'|'warning'|'info'|'success'} props.color - Button color
 * @param {boolean} props.disabled - Whether button is disabled
 * @param {string} props.ariaLabel - Accessibility label
 * @param {function} props.onClick - Click handler
 * @param {React.ReactNode} props.children - Icon element
 */
const ButtonIcon = ({
  size = 'medium',
  color = 'primary',
  disabled = false,
  ariaLabel,
  onClick,
  children,
  ...props
}) => {
  return (
    <StyledIconButton
      size={size}
      color={color}
      disabled={disabled}
      aria-label={ariaLabel}
      onClick={onClick}
      {...props}
    >
      {children}
    </StyledIconButton>
  );
};

Button.Icon = ButtonIcon;

export default Button;
