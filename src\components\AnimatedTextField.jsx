// src/components/AnimatedTextField.jsx
import React from 'react';
import TextField from '@mui/material/TextField';
import { styled, keyframes } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';


// Define the rainbow animation
const rainbowRotate = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

// Styled Wrapper Div
const AnimatedWrapper = styled('div')(({ theme }) => ({
    position: 'relative',
    borderRadius: theme.shape.borderRadius, // Use theme border radius
    padding: '1px', // Padding to allow border to show inside
    overflow: 'hidden', // Hide overflow of pseudo elements
    transition: `padding 0.3s cubic-bezier(0.4, 0, 0.2, 1)`, // Use design system easing

    // Pseudo element for the animated background conic gradient
    '&::before': {
        content: '""',
        display: 'block',
        position: 'absolute',
        width: '250%', // Make gradient large enough to cover corners during rotation
        height: '250%', // Adjust size as needed
        top: '-75%', // Position relative to center
        left: '-75%', // Position relative to center
        // The rainbow gradient using theme colors
        backgroundImage: `conic-gradient(from 0deg at 50% 50%, transparent 0%, ${theme.palette.warning.main}, ${theme.palette.success.main}, ${theme.palette.info.main}, ${theme.palette.primary.main}, ${theme.palette.secondary.main}, ${theme.palette.warning.main}, transparent 100%)`,
        filter: 'blur(5px)', // Subtle blur on gradient itself
        animation: `${rainbowRotate} 4s linear infinite`,
        zIndex: 0, // Behind the input
        opacity: 0, // Hidden by default
        transition: `opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1)`, // Use design system easing
    },

    // Apply styles when the TextField INSIDE is focused
    '&:focus-within': {
        padding: '2px', // Increase padding slightly to reveal border? Or just show opacity
        '&::before': {
            opacity: 1, // Show the rainbow gradient background
        },
    },

    // Style the TextField itself to sit 'above' the gradient background
    '& .MuiTextField-root': {
        position: 'relative',
        zIndex: 1, // Ensure TextField is above the ::before pseudo-element
        // Make TextField background transparent ONLY when focused? or match theme paper
         backgroundColor: theme.palette.background.paper, // Match theme background
         borderRadius: 'inherit', // Ensure inner border radius matches wrapper
    },
     // Adjust MUI styles to rely on wrapper for border/focus
    '& .MuiOutlinedInput-root': {
         borderRadius: 'inherit', // Ensure root inherits wrapper radius
         backgroundColor: theme.palette.background.paper, // Set bg here too
         '& fieldset': {
              border: 'none', // Remove the default MUI border
              // Or set to transparent initially and primary on focus-within wrapper?
              // borderColor: 'transparent !important'
         },
         '&.Mui-focused fieldset': {
             border: 'none', // Keep border none on focus too
         },
         '&:hover fieldset': {
              border: 'none', // No border change on hover
         },
    },
}));

// Wrapper Component
const AnimatedTextField = React.forwardRef((props, ref) => {
    return (
        <AnimatedWrapper>
            <TextField
                ref={ref}
                variant="outlined" // Standard variant
                fullWidth // Assume fullWidth usually needed
                {...props} // Pass all other props through
                // Override internal styles slightly if needed
                InputProps={{
                    ...props.InputProps,
                     sx:{ borderRadius: 'inherit', ...props.InputProps?.sx} // Ensure Input inherits radius
                }}
                 sx={{borderRadius:'inherit', ...props.sx}} // Ensure root inherits
            />
        </AnimatedWrapper>
    );
});

AnimatedTextField.displayName = 'AnimatedTextField'; // Add display name for DevTools
export default AnimatedTextField;
