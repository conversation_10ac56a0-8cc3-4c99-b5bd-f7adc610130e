{"D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\_utils\\nhost.ts": {"path": "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\_utils\\nhost.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 4, "column": 13}, "end": {"line": 8, "column": 3}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 40}}, {"start": {"line": 5, "column": 44}, "end": {"line": 5, "column": 51}}]}, "1": {"loc": {"start": {"line": 6, "column": 10}, "end": {"line": 6, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 10}, "end": {"line": 6, "column": 34}}, {"start": {"line": 6, "column": 38}, "end": {"line": 6, "column": 52}}]}}, "s": {"0": 0, "1": 0}, "f": {}, "b": {"0": [0, 0], "1": [0, 0]}}, "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\credit\\handleCreditTransaction.ts": {"path": "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\credit\\handleCreditTransaction.ts", "statementMap": {"0": {"start": {"line": 2, "column": 16}, "end": {"line": 2, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 40}}, "4": {"start": {"line": 20, "column": 24}, "end": {"line": 33, "column": 2}}, "5": {"start": {"line": 38, "column": 29}, "end": {"line": 63, "column": 2}}, "6": {"start": {"line": 68, "column": 27}, "end": {"line": 88, "column": 2}}, "7": {"start": {"line": 93, "column": 34}, "end": {"line": 117, "column": 2}}, "8": {"start": {"line": 122, "column": 26}, "end": {"line": 135, "column": 2}}, "9": {"start": {"line": 142, "column": 39}, "end": {"line": 340, "column": 1}}, "10": {"start": {"line": 146, "column": 2}, "end": {"line": 339, "column": 3}}, "11": {"start": {"line": 147, "column": 88}, "end": {"line": 147, "column": 96}}, "12": {"start": {"line": 150, "column": 4}, "end": {"line": 156, "column": 5}}, "13": {"start": {"line": 151, "column": 6}, "end": {"line": 155, "column": 8}}, "14": {"start": {"line": 158, "column": 4}, "end": {"line": 164, "column": 5}}, "15": {"start": {"line": 159, "column": 6}, "end": {"line": 163, "column": 8}}, "16": {"start": {"line": 167, "column": 33}, "end": {"line": 169, "column": 6}}, "17": {"start": {"line": 171, "column": 4}, "end": {"line": 177, "column": 5}}, "18": {"start": {"line": 172, "column": 6}, "end": {"line": 176, "column": 8}}, "19": {"start": {"line": 179, "column": 35}, "end": {"line": 179, "column": 60}}, "20": {"start": {"line": 180, "column": 28}, "end": {"line": 180, "column": 33}}, "21": {"start": {"line": 181, "column": 23}, "end": {"line": 181, "column": 49}}, "22": {"start": {"line": 182, "column": 27}, "end": {"line": 182, "column": 58}}, "23": {"start": {"line": 183, "column": 29}, "end": {"line": 183, "column": 61}}, "24": {"start": {"line": 186, "column": 4}, "end": {"line": 207, "column": 5}}, "25": {"start": {"line": 187, "column": 33}, "end": {"line": 189, "column": 8}}, "26": {"start": {"line": 191, "column": 6}, "end": {"line": 206, "column": 7}}, "27": {"start": {"line": 192, "column": 35}, "end": {"line": 192, "column": 61}}, "28": {"start": {"line": 195, "column": 20}, "end": {"line": 195, "column": 30}}, "29": {"start": {"line": 196, "column": 24}, "end": {"line": 196, "column": 51}}, "30": {"start": {"line": 198, "column": 8}, "end": {"line": 205, "column": 9}}, "31": {"start": {"line": 199, "column": 37}, "end": {"line": 199, "column": 55}}, "32": {"start": {"line": 202, "column": 10}, "end": {"line": 204, "column": 11}}, "33": {"start": {"line": 203, "column": 12}, "end": {"line": 203, "column": 37}}, "34": {"start": {"line": 210, "column": 4}, "end": {"line": 288, "column": 5}}, "35": {"start": {"line": 212, "column": 8}, "end": {"line": 218, "column": 9}}, "36": {"start": {"line": 213, "column": 10}, "end": {"line": 217, "column": 12}}, "37": {"start": {"line": 221, "column": 37}, "end": {"line": 223, "column": 10}}, "38": {"start": {"line": 225, "column": 8}, "end": {"line": 231, "column": 9}}, "39": {"start": {"line": 226, "column": 10}, "end": {"line": 230, "column": 12}}, "40": {"start": {"line": 234, "column": 8}, "end": {"line": 234, "column": 59}}, "41": {"start": {"line": 235, "column": 8}, "end": {"line": 235, "column": 14}}, "42": {"start": {"line": 239, "column": 8}, "end": {"line": 260, "column": 9}}, "43": {"start": {"line": 241, "column": 10}, "end": {"line": 259, "column": 11}}, "44": {"start": {"line": 242, "column": 12}, "end": {"line": 248, "column": 13}}, "45": {"start": {"line": 243, "column": 14}, "end": {"line": 247, "column": 16}}, "46": {"start": {"line": 249, "column": 12}, "end": {"line": 249, "column": 72}}, "47": {"start": {"line": 251, "column": 12}, "end": {"line": 257, "column": 13}}, "48": {"start": {"line": 252, "column": 14}, "end": {"line": 256, "column": 16}}, "49": {"start": {"line": 258, "column": 12}, "end": {"line": 258, "column": 63}}, "50": {"start": {"line": 261, "column": 8}, "end": {"line": 261, "column": 14}}, "51": {"start": {"line": 265, "column": 8}, "end": {"line": 265, "column": 54}}, "52": {"start": {"line": 266, "column": 8}, "end": {"line": 266, "column": 59}}, "53": {"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 14}}, "54": {"start": {"line": 271, "column": 8}, "end": {"line": 271, "column": 59}}, "55": {"start": {"line": 272, "column": 8}, "end": {"line": 272, "column": 14}}, "56": {"start": {"line": 275, "column": 8}, "end": {"line": 279, "column": 9}}, "57": {"start": {"line": 276, "column": 10}, "end": {"line": 276, "column": 70}}, "58": {"start": {"line": 278, "column": 10}, "end": {"line": 278, "column": 61}}, "59": {"start": {"line": 280, "column": 8}, "end": {"line": 280, "column": 14}}, "60": {"start": {"line": 283, "column": 8}, "end": {"line": 287, "column": 10}}, "61": {"start": {"line": 291, "column": 4}, "end": {"line": 298, "column": 5}}, "62": {"start": {"line": 292, "column": 6}, "end": {"line": 297, "column": 9}}, "63": {"start": {"line": 301, "column": 26}, "end": {"line": 301, "column": 34}}, "64": {"start": {"line": 302, "column": 4}, "end": {"line": 310, "column": 7}}, "65": {"start": {"line": 313, "column": 48}, "end": {"line": 320, "column": 6}}, "66": {"start": {"line": 322, "column": 4}, "end": {"line": 322, "column": 35}}, "67": {"start": {"line": 324, "column": 4}, "end": {"line": 324, "column": 54}}, "68": {"start": {"line": 326, "column": 4}, "end": {"line": 338, "column": 5}}, "69": {"start": {"line": 327, "column": 6}, "end": {"line": 331, "column": 9}}, "70": {"start": {"line": 333, "column": 6}, "end": {"line": 337, "column": 9}}, "71": {"start": {"line": 142, "column": 13}, "end": {"line": 142, "column": 39}}, "72": {"start": {"line": 345, "column": 0}, "end": {"line": 351, "column": 2}}, "73": {"start": {"line": 346, "column": 2}, "end": {"line": 348, "column": 3}}, "74": {"start": {"line": 347, "column": 4}, "end": {"line": 347, "column": 83}}, "75": {"start": {"line": 350, "column": 2}, "end": {"line": 350, "column": 42}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 142, "column": 39}, "end": {"line": 142, "column": 44}}, "loc": {"start": {"line": 145, "column": 19}, "end": {"line": 340, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 345, "column": 15}, "end": {"line": 345, "column": 20}}, "loc": {"start": {"line": 345, "column": 68}, "end": {"line": 351, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 150, "column": 4}, "end": {"line": 156, "column": 5}}, "type": "if", "locations": [{"start": {"line": 150, "column": 4}, "end": {"line": 156, "column": 5}}]}, "1": {"loc": {"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 8}, "end": {"line": 150, "column": 19}}, {"start": {"line": 150, "column": 23}, "end": {"line": 150, "column": 71}}]}, "2": {"loc": {"start": {"line": 158, "column": 4}, "end": {"line": 164, "column": 5}}, "type": "if", "locations": [{"start": {"line": 158, "column": 4}, "end": {"line": 164, "column": 5}}]}, "3": {"loc": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 19}}, {"start": {"line": 158, "column": 23}, "end": {"line": 158, "column": 71}}]}, "4": {"loc": {"start": {"line": 171, "column": 4}, "end": {"line": 177, "column": 5}}, "type": "if", "locations": [{"start": {"line": 171, "column": 4}, "end": {"line": 177, "column": 5}}]}, "5": {"loc": {"start": {"line": 171, "column": 8}, "end": {"line": 171, "column": 85}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 8}, "end": {"line": 171, "column": 19}}, {"start": {"line": 171, "column": 23}, "end": {"line": 171, "column": 46}}, {"start": {"line": 171, "column": 50}, "end": {"line": 171, "column": 85}}]}, "6": {"loc": {"start": {"line": 186, "column": 4}, "end": {"line": 207, "column": 5}}, "type": "if", "locations": [{"start": {"line": 186, "column": 4}, "end": {"line": 207, "column": 5}}]}, "7": {"loc": {"start": {"line": 191, "column": 6}, "end": {"line": 206, "column": 7}}, "type": "if", "locations": [{"start": {"line": 191, "column": 6}, "end": {"line": 206, "column": 7}}]}, "8": {"loc": {"start": {"line": 191, "column": 10}, "end": {"line": 191, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 191, "column": 10}, "end": {"line": 191, "column": 18}}, {"start": {"line": 191, "column": 22}, "end": {"line": 191, "column": 48}}]}, "9": {"loc": {"start": {"line": 198, "column": 8}, "end": {"line": 205, "column": 9}}, "type": "if", "locations": [{"start": {"line": 198, "column": 8}, "end": {"line": 205, "column": 9}}]}, "10": {"loc": {"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 40}}, {"start": {"line": 198, "column": 44}, "end": {"line": 198, "column": 57}}]}, "11": {"loc": {"start": {"line": 202, "column": 10}, "end": {"line": 204, "column": 11}}, "type": "if", "locations": [{"start": {"line": 202, "column": 10}, "end": {"line": 204, "column": 11}}]}, "12": {"loc": {"start": {"line": 202, "column": 14}, "end": {"line": 202, "column": 87}}, "type": "binary-expr", "locations": [{"start": {"line": 202, "column": 14}, "end": {"line": 202, "column": 35}}, {"start": {"line": 202, "column": 39}, "end": {"line": 202, "column": 87}}]}, "13": {"loc": {"start": {"line": 210, "column": 4}, "end": {"line": 288, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 211, "column": 6}, "end": {"line": 235, "column": 14}}, {"start": {"line": 237, "column": 6}, "end": {"line": 261, "column": 14}}, {"start": {"line": 263, "column": 6}, "end": {"line": 267, "column": 14}}, {"start": {"line": 269, "column": 6}, "end": {"line": 269, "column": 47}}, {"start": {"line": 270, "column": 6}, "end": {"line": 272, "column": 14}}, {"start": {"line": 274, "column": 6}, "end": {"line": 280, "column": 14}}, {"start": {"line": 282, "column": 6}, "end": {"line": 287, "column": 10}}]}, "14": {"loc": {"start": {"line": 212, "column": 8}, "end": {"line": 218, "column": 9}}, "type": "if", "locations": [{"start": {"line": 212, "column": 8}, "end": {"line": 218, "column": 9}}]}, "15": {"loc": {"start": {"line": 225, "column": 8}, "end": {"line": 231, "column": 9}}, "type": "if", "locations": [{"start": {"line": 225, "column": 8}, "end": {"line": 231, "column": 9}}]}, "16": {"loc": {"start": {"line": 225, "column": 12}, "end": {"line": 225, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 225, "column": 12}, "end": {"line": 225, "column": 23}}, {"start": {"line": 225, "column": 27}, "end": {"line": 225, "column": 59}}]}, "17": {"loc": {"start": {"line": 239, "column": 8}, "end": {"line": 260, "column": 9}}, "type": "if", "locations": [{"start": {"line": 239, "column": 8}, "end": {"line": 260, "column": 9}}]}, "18": {"loc": {"start": {"line": 241, "column": 10}, "end": {"line": 259, "column": 11}}, "type": "if", "locations": [{"start": {"line": 241, "column": 10}, "end": {"line": 259, "column": 11}}, {"start": {"line": 250, "column": 17}, "end": {"line": 259, "column": 11}}]}, "19": {"loc": {"start": {"line": 242, "column": 12}, "end": {"line": 248, "column": 13}}, "type": "if", "locations": [{"start": {"line": 242, "column": 12}, "end": {"line": 248, "column": 13}}]}, "20": {"loc": {"start": {"line": 251, "column": 12}, "end": {"line": 257, "column": 13}}, "type": "if", "locations": [{"start": {"line": 251, "column": 12}, "end": {"line": 257, "column": 13}}]}, "21": {"loc": {"start": {"line": 275, "column": 8}, "end": {"line": 279, "column": 9}}, "type": "if", "locations": [{"start": {"line": 275, "column": 8}, "end": {"line": 279, "column": 9}}, {"start": {"line": 277, "column": 15}, "end": {"line": 279, "column": 9}}]}, "22": {"loc": {"start": {"line": 291, "column": 4}, "end": {"line": 298, "column": 5}}, "type": "if", "locations": [{"start": {"line": 291, "column": 4}, "end": {"line": 298, "column": 5}}]}, "23": {"loc": {"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 78}}, "type": "binary-expr", "locations": [{"start": {"line": 291, "column": 8}, "end": {"line": 291, "column": 26}}, {"start": {"line": 291, "column": 30}, "end": {"line": 291, "column": 78}}]}, "24": {"loc": {"start": {"line": 307, "column": 20}, "end": {"line": 307, "column": 94}}, "type": "cond-expr", "locations": [{"start": {"line": 307, "column": 63}, "end": {"line": 307, "column": 79}}, {"start": {"line": 307, "column": 82}, "end": {"line": 307, "column": 94}}]}, "25": {"loc": {"start": {"line": 326, "column": 4}, "end": {"line": 338, "column": 5}}, "type": "if", "locations": [{"start": {"line": 326, "column": 4}, "end": {"line": 338, "column": 5}}, {"start": {"line": 332, "column": 11}, "end": {"line": 338, "column": 5}}]}, "26": {"loc": {"start": {"line": 346, "column": 2}, "end": {"line": 348, "column": 3}}, "type": "if", "locations": [{"start": {"line": 346, "column": 2}, "end": {"line": 348, "column": 3}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0], "5": [0, 0, 0], "6": [0], "7": [0], "8": [0, 0], "9": [0], "10": [0, 0], "11": [0], "12": [0, 0], "13": [0, 0, 0, 0, 0, 0, 0], "14": [0], "15": [0], "16": [0, 0], "17": [0], "18": [0, 0], "19": [0], "20": [0], "21": [0, 0], "22": [0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0]}}, "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\credit\\handleDailyCredits.ts": {"path": "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\credit\\handleDailyCredits.ts", "statementMap": {"0": {"start": {"line": 2, "column": 16}, "end": {"line": 2, "column": 42}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 26}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 40}}, "4": {"start": {"line": 17, "column": 34}, "end": {"line": 58, "column": 2}}, "5": {"start": {"line": 64, "column": 31}, "end": {"line": 66, "column": 1}}, "6": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 13}}, "7": {"start": {"line": 73, "column": 34}, "end": {"line": 87, "column": 1}}, "8": {"start": {"line": 74, "column": 2}, "end": {"line": 76, "column": 3}}, "9": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 16}}, "10": {"start": {"line": 78, "column": 19}, "end": {"line": 78, "column": 47}}, "11": {"start": {"line": 79, "column": 14}, "end": {"line": 79, "column": 24}}, "12": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 32}}, "13": {"start": {"line": 83, "column": 16}, "end": {"line": 83, "column": 26}}, "14": {"start": {"line": 84, "column": 2}, "end": {"line": 84, "column": 29}}, "15": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 26}}, "16": {"start": {"line": 94, "column": 34}, "end": {"line": 192, "column": 1}}, "17": {"start": {"line": 98, "column": 2}, "end": {"line": 191, "column": 3}}, "18": {"start": {"line": 99, "column": 23}, "end": {"line": 99, "column": 31}}, "19": {"start": {"line": 101, "column": 4}, "end": {"line": 107, "column": 5}}, "20": {"start": {"line": 102, "column": 6}, "end": {"line": 106, "column": 9}}, "21": {"start": {"line": 110, "column": 21}, "end": {"line": 112, "column": 6}}, "22": {"start": {"line": 114, "column": 4}, "end": {"line": 120, "column": 5}}, "23": {"start": {"line": 115, "column": 6}, "end": {"line": 119, "column": 9}}, "24": {"start": {"line": 122, "column": 35}, "end": {"line": 122, "column": 54}}, "25": {"start": {"line": 123, "column": 35}, "end": {"line": 123, "column": 57}}, "26": {"start": {"line": 126, "column": 4}, "end": {"line": 132, "column": 5}}, "27": {"start": {"line": 127, "column": 6}, "end": {"line": 131, "column": 9}}, "28": {"start": {"line": 135, "column": 28}, "end": {"line": 135, "column": 52}}, "29": {"start": {"line": 136, "column": 19}, "end": {"line": 136, "column": 43}}, "30": {"start": {"line": 139, "column": 4}, "end": {"line": 150, "column": 5}}, "31": {"start": {"line": 140, "column": 30}, "end": {"line": 144, "column": 8}}, "32": {"start": {"line": 141, "column": 28}, "end": {"line": 141, "column": 64}}, "33": {"start": {"line": 142, "column": 31}, "end": {"line": 142, "column": 70}}, "34": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 61}}, "35": {"start": {"line": 146, "column": 6}, "end": {"line": 149, "column": 7}}, "36": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": 72}}, "37": {"start": {"line": 148, "column": 8}, "end": {"line": 148, "column": 52}}, "38": {"start": {"line": 153, "column": 57}, "end": {"line": 157, "column": 6}}, "39": {"start": {"line": 160, "column": 24}, "end": {"line": 160, "column": 82}}, "40": {"start": {"line": 161, "column": 21}, "end": {"line": 169, "column": null}}, "41": {"start": {"line": 172, "column": 4}, "end": {"line": 182, "column": 5}}, "42": {"start": {"line": 173, "column": 6}, "end": {"line": 179, "column": 9}}, "43": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": 62}}, "44": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": 61}}, "45": {"start": {"line": 186, "column": 4}, "end": {"line": 190, "column": 7}}, "46": {"start": {"line": 94, "column": 13}, "end": {"line": 94, "column": 34}}, "47": {"start": {"line": 197, "column": 0}, "end": {"line": 203, "column": 2}}, "48": {"start": {"line": 198, "column": 2}, "end": {"line": 200, "column": 3}}, "49": {"start": {"line": 199, "column": 4}, "end": {"line": 199, "column": 83}}, "50": {"start": {"line": 202, "column": 2}, "end": {"line": 202, "column": 37}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 64, "column": 31}, "end": {"line": 64, "column": 42}}, "loc": {"start": {"line": 64, "column": 44}, "end": {"line": 66, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 73, "column": 34}, "end": {"line": 73, "column": 35}}, "loc": {"start": {"line": 73, "column": 81}, "end": {"line": 87, "column": 1}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 94, "column": 34}, "end": {"line": 94, "column": 39}}, "loc": {"start": {"line": 97, "column": 19}, "end": {"line": 192, "column": 1}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 140, "column": 48}, "end": {"line": 140, "column": 49}}, "loc": {"start": {"line": 140, "column": 66}, "end": {"line": 144, "column": 7}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 197, "column": 15}, "end": {"line": 197, "column": 20}}, "loc": {"start": {"line": 197, "column": 68}, "end": {"line": 203, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 74, "column": 2}, "end": {"line": 76, "column": 3}}, "type": "if", "locations": [{"start": {"line": 74, "column": 2}, "end": {"line": 76, "column": 3}}]}, "1": {"loc": {"start": {"line": 101, "column": 4}, "end": {"line": 107, "column": 5}}, "type": "if", "locations": [{"start": {"line": 101, "column": 4}, "end": {"line": 107, "column": 5}}]}, "2": {"loc": {"start": {"line": 114, "column": 4}, "end": {"line": 120, "column": 5}}, "type": "if", "locations": [{"start": {"line": 114, "column": 4}, "end": {"line": 120, "column": 5}}]}, "3": {"loc": {"start": {"line": 114, "column": 8}, "end": {"line": 114, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 8}, "end": {"line": 114, "column": 13}}, {"start": {"line": 114, "column": 17}, "end": {"line": 114, "column": 34}}, {"start": {"line": 114, "column": 38}, "end": {"line": 114, "column": 67}}]}, "4": {"loc": {"start": {"line": 123, "column": 35}, "end": {"line": 123, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 123, "column": 35}, "end": {"line": 123, "column": 51}}, {"start": {"line": 123, "column": 55}, "end": {"line": 123, "column": 57}}]}, "5": {"loc": {"start": {"line": 126, "column": 4}, "end": {"line": 132, "column": 5}}, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 132, "column": 5}}]}, "6": {"loc": {"start": {"line": 139, "column": 4}, "end": {"line": 150, "column": 5}}, "type": "if", "locations": [{"start": {"line": 139, "column": 4}, "end": {"line": 150, "column": 5}}]}, "7": {"loc": {"start": {"line": 141, "column": 28}, "end": {"line": 141, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 141, "column": 28}, "end": {"line": 141, "column": 59}}, {"start": {"line": 141, "column": 63}, "end": {"line": 141, "column": 64}}]}, "8": {"loc": {"start": {"line": 142, "column": 31}, "end": {"line": 142, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 31}, "end": {"line": 142, "column": 65}}, {"start": {"line": 142, "column": 69}, "end": {"line": 142, "column": 70}}]}, "9": {"loc": {"start": {"line": 143, "column": 15}, "end": {"line": 143, "column": 60}}, "type": "cond-expr", "locations": [{"start": {"line": 143, "column": 46}, "end": {"line": 143, "column": 50}}, {"start": {"line": 143, "column": 53}, "end": {"line": 143, "column": 60}}]}, "10": {"loc": {"start": {"line": 146, "column": 6}, "end": {"line": 149, "column": 7}}, "type": "if", "locations": [{"start": {"line": 146, "column": 6}, "end": {"line": 149, "column": 7}}]}, "11": {"loc": {"start": {"line": 146, "column": 10}, "end": {"line": 146, "column": 104}}, "type": "binary-expr", "locations": [{"start": {"line": 146, "column": 10}, "end": {"line": 146, "column": 37}}, {"start": {"line": 146, "column": 41}, "end": {"line": 146, "column": 104}}]}, "12": {"loc": {"start": {"line": 160, "column": 24}, "end": {"line": 160, "column": 82}}, "type": "binary-expr", "locations": [{"start": {"line": 160, "column": 24}, "end": {"line": 160, "column": 55}}, {"start": {"line": 160, "column": 59}, "end": {"line": 160, "column": 82}}]}, "13": {"loc": {"start": {"line": 167, "column": 27}, "end": {"line": 167, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 167, "column": 27}, "end": {"line": 167, "column": 52}}, {"start": {"line": 167, "column": 56}, "end": {"line": 167, "column": 58}}]}, "14": {"loc": {"start": {"line": 172, "column": 4}, "end": {"line": 182, "column": 5}}, "type": "if", "locations": [{"start": {"line": 172, "column": 4}, "end": {"line": 182, "column": 5}}, {"start": {"line": 180, "column": 11}, "end": {"line": 182, "column": 5}}]}, "15": {"loc": {"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 172, "column": 8}, "end": {"line": 172, "column": 31}}, {"start": {"line": 172, "column": 35}, "end": {"line": 172, "column": 56}}]}, "16": {"loc": {"start": {"line": 188, "column": 15}, "end": {"line": 188, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 188, "column": 40}, "end": {"line": 188, "column": 53}}, {"start": {"line": 188, "column": 56}, "end": {"line": 188, "column": 86}}]}, "17": {"loc": {"start": {"line": 198, "column": 2}, "end": {"line": 200, "column": 3}}, "type": "if", "locations": [{"start": {"line": 198, "column": 2}, "end": {"line": 200, "column": 3}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 2, "7": 1, "8": 3, "9": 2, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 3, "18": 3, "19": 3, "20": 0, "21": 3, "22": 3, "23": 0, "24": 3, "25": 3, "26": 3, "27": 1, "28": 2, "29": 2, "30": 2, "31": 1, "32": 0, "33": 0, "34": 0, "35": 1, "36": 1, "37": 1, "38": 2, "39": 2, "40": 2, "41": 2, "42": 2, "43": 0, "44": 0, "45": 0, "46": 1, "47": 1, "48": 0, "49": 0, "50": 0}, "f": {"0": 2, "1": 3, "2": 3, "3": 0, "4": 0}, "b": {"0": [2], "1": [0], "2": [0], "3": [3, 3, 3], "4": [3, 0], "5": [1], "6": [1], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [1], "11": [1, 1], "12": [2, 0], "13": [2, 0], "14": [2, 0], "15": [2, 2], "16": [0, 0], "17": [0]}}, "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\credit\\index.ts": {"path": "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\credit\\index.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 68}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 58}}, "2": {"start": {"line": 8, "column": 0}, "end": {"line": 22, "column": 2}}, "3": {"start": {"line": 9, "column": 15}, "end": {"line": 9, "column": 23}}, "4": {"start": {"line": 12, "column": 2}, "end": {"line": 21, "column": 3}}, "5": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 44}}, "6": {"start": {"line": 14, "column": 9}, "end": {"line": 21, "column": 3}}, "7": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 39}}, "8": {"start": {"line": 17, "column": 4}, "end": {"line": 20, "column": 7}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 20}}, "loc": {"start": {"line": 8, "column": 68}, "end": {"line": 22, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 21, "column": 3}}, "type": "if", "locations": [{"start": {"line": 12, "column": 2}, "end": {"line": 21, "column": 3}}, {"start": {"line": 14, "column": 9}, "end": {"line": 21, "column": 3}}]}, "1": {"loc": {"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 6}, "end": {"line": 12, "column": 29}}, {"start": {"line": 12, "column": 33}, "end": {"line": 12, "column": 68}}]}, "2": {"loc": {"start": {"line": 14, "column": 9}, "end": {"line": 21, "column": 3}}, "type": "if", "locations": [{"start": {"line": 14, "column": 9}, "end": {"line": 21, "column": 3}}, {"start": {"line": 16, "column": 9}, "end": {"line": 21, "column": 3}}]}, "3": {"loc": {"start": {"line": 14, "column": 13}, "end": {"line": 14, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 13}, "end": {"line": 14, "column": 30}}, {"start": {"line": 14, "column": 34}, "end": {"line": 14, "column": 64}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 4, "4": 4, "5": 0, "6": 4, "7": 3, "8": 1}, "f": {"0": 4}, "b": {"0": [0, 4], "1": [4, 4], "2": [3, 1], "3": [4, 2]}}, "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\credit\\types.ts": {"path": "D:\\AiGeniusSpace\\aigenius-app-nhost\\functions\\credit\\types.ts", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": null}}, "1": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": null}}, "2": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "3": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "4": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": null}}, "5": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": null}}, "6": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "7": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": null}}, "8": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "9": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": null}}, "10": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": null}}, "11": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": null}}, "12": {"start": {"line": 133, "column": 2}, "end": {"line": 133, "column": null}}, "13": {"start": {"line": 134, "column": 2}, "end": {"line": 134, "column": null}}, "14": {"start": {"line": 135, "column": 2}, "end": {"line": 135, "column": null}}, "15": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": null}}, "16": {"start": {"line": 137, "column": 2}, "end": {"line": 137, "column": null}}, "17": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": null}}, "18": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": null}}, "19": {"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": null}}, "20": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 19}}, "21": {"start": {"line": 147, "column": 2}, "end": {"line": 147, "column": null}}, "22": {"start": {"line": 148, "column": 2}, "end": {"line": 148, "column": null}}, "23": {"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": 21}}, "24": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 33}}, "25": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 30}}, "26": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 12}}, "loc": {"start": {"line": 6, "column": 27}, "end": {"line": 13, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 12}}, "loc": {"start": {"line": 18, "column": 27}, "end": {"line": 22, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 12}}, "loc": {"start": {"line": 132, "column": 27}, "end": {"line": 141, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 150, "column": 2}, "end": {"line": 150, "column": 14}}, "loc": {"start": {"line": 150, "column": 78}, "end": {"line": 155, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 27}}, {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": null}}]}, "1": {"loc": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 27}}, {"start": {"line": 18, "column": 27}, "end": {"line": 18, "column": null}}]}, "2": {"loc": {"start": {"line": 132, "column": 12}, "end": {"line": 132, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 132, "column": 12}, "end": {"line": 132, "column": 27}}, {"start": {"line": 132, "column": 27}, "end": {"line": 132, "column": null}}]}, "3": {"loc": {"start": {"line": 150, "column": 54}, "end": {"line": 150, "column": 78}}, "type": "default-arg", "locations": [{"start": {"line": 150, "column": 75}, "end": {"line": 150, "column": 78}}]}}, "s": {"0": 3, "1": 3, "2": 3, "3": 3, "4": 3, "5": 3, "6": 3, "7": 3, "8": 3, "9": 3, "10": 3, "11": 3, "12": 3, "13": 3, "14": 3, "15": 3, "16": 3, "17": 3, "18": 3, "19": 3, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 3}, "f": {"0": 3, "1": 3, "2": 3, "3": 0}, "b": {"0": [3, 3], "1": [3, 3], "2": [3, 3], "3": [0]}}}