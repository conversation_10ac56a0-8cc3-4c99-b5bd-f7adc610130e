// src/components/design-system/Feedback/Badge.jsx
import React from 'react';
import { Chip, Badge as MuiBadge } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledChip = styled(Chip)(({ theme, size, variant }) => ({
  // Size variants
  ...(size === 'small' && {
    height: 20,
    fontSize: '0.6875rem',
    '& .MuiChip-label': {
      paddingLeft: 6,
      paddingRight: 6,
    },
  }),
  ...(size === 'medium' && {
    height: 24,
    fontSize: '0.75rem',
  }),
  ...(size === 'large' && {
    height: 32,
    fontSize: '0.875rem',
  }),
  
  // Variant styles
  ...(variant === 'dot' && {
    '& .MuiChip-label': {
      display: 'none',
    },
    width: 8,
    height: 8,
    borderRadius: '50%',
  }),
}));

/**
 * Badge component for status indicators and labels
 * 
 * @param {Object} props
 * @param {'default'|'primary'|'secondary'|'error'|'warning'|'info'|'success'} props.color - Badge color
 * @param {'filled'|'outlined'|'dot'} props.variant - Badge variant
 * @param {'small'|'medium'|'large'} props.size - Badge size
 * @param {React.ReactNode} props.children - Badge content
 */
const Badge = ({ 
  color = 'default',
  variant = 'filled',
  size = 'medium',
  children,
  ...props 
}) => {
  return (
    <StyledChip
      label={children}
      color={color}
      variant={variant === 'outlined' ? 'outlined' : 'filled'}
      size={size}
      {...props}
    />
  );
};

/**
 * NotificationBadge component for notification indicators
 * 
 * @param {Object} props
 * @param {number} props.count - Notification count
 * @param {number} props.max - Maximum count to display
 * @param {boolean} props.showZero - Whether to show badge when count is 0
 * @param {'primary'|'secondary'|'error'|'warning'|'info'|'success'} props.color - Badge color
 * @param {React.ReactNode} props.children - Element to badge
 */
const NotificationBadge = ({ 
  count = 0,
  max = 99,
  showZero = false,
  color = 'error',
  children,
  ...props 
}) => {
  return (
    <MuiBadge
      badgeContent={count}
      max={max}
      showZero={showZero}
      color={color}
      {...props}
    >
      {children}
    </MuiBadge>
  );
};

Badge.Notification = NotificationBadge;

export default Badge;
