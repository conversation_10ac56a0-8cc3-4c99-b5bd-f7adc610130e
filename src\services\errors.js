// src/services/errors.js
export class GenerationServiceError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'GenerationServiceError';
    this.originalError = originalError;
  }
}

export class GraphQLServiceError extends Error {
  constructor(message, graphqlErrors = null, originalError = null) {
    super(message);
    this.name = 'GraphQLServiceError';
    this.graphqlErrors = graphqlErrors;
    this.originalError = originalError;
  }
}

export class CreditServiceError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'CreditServiceError';
    this.originalError = originalError;
  }
}

export class PassServiceError extends Error {
  constructor(message, originalError = null) {
    super(message);
    this.name = 'PassServiceError';
    this.originalError = originalError;
  }
}

export class WalletNotFoundError extends CreditServiceError {
  constructor(userId) {
    super(`Wallet not found for user: ${userId}`);
    this.name = 'WalletNotFoundError';
    this.userId = userId;
  }
}

export class InsufficientCreditsError extends CreditServiceError {
  constructor(required, available) {
    super(`Insufficient credits: required ${required}, available ${available}`);
    this.name = 'InsufficientCreditsError';
    this.required = required;
    this.available = available;
  }
}

export class ServerAccessError extends Error {
  constructor(message, serverType = null, userTier = null) {
    super(message);
    this.name = 'ServerAccessError';
    this.serverType = serverType;
    this.userTier = userTier;
  }
}