// src/utils/authDebug.js
// Debug utility for authentication flow

import { nhost } from '../services/nhost';

/**
 * Debug authentication state and provide detailed logging
 */
export const debugAuthState = () => {
  const session = nhost.auth.getSession();
  const user = nhost.auth.getUser();
  const isAuthenticated = nhost.auth.isAuthenticated();

  console.group('🔍 Authentication Debug Info');
  console.log('📊 Current State:', {
    isAuthenticated,
    hasSession: !!session,
    hasUser: !!user,
    timestamp: new Date().toISOString()
  });

  if (session) {
    console.log('🎫 Session Details:', {
      accessToken: session.accessToken ? '✅ Present' : '❌ Missing',
      refreshToken: session.refreshToken ? '✅ Present' : '❌ Missing',
      accessTokenExpiresIn: session.accessTokenExpiresIn ? new Date(session.accessTokenExpiresIn).toLocaleString() : 'Not set',
      refreshTokenExpiresIn: session.refreshTokenExpiresIn ? new Date(session.refreshTokenExpiresIn).toLocaleString() : 'Not set'
    });
  }

  if (user) {
    console.log('👤 User Details:', {
      id: user.id,
      email: user.email,
      emailVerified: user.emailVerified,
      displayName: user.displayName,
      defaultRole: user.defaultRole,
      roles: user.roles?.map(r => r.role) || []
    });
  }

  // Check stored emails
  const storedEmails = {
    pendingVerificationEmail: localStorage.getItem('pendingVerificationEmail'),
    registrationEmail: sessionStorage.getItem('registrationEmail'),
    rememberedEmail: localStorage.getItem('rememberedEmail'),
    postVerificationRedirect: localStorage.getItem('postVerificationRedirect')
  };

  console.log('📧 Stored Email Data:', storedEmails);
  console.groupEnd();

  return {
    isAuthenticated,
    session,
    user,
    storedEmails
  };
};

/**
 * Test email verification flow
 */
export const testVerificationFlow = async (email) => {
  console.group('🧪 Testing Verification Flow');
  
  try {
    // Try to resend verification email
    console.log('📤 Attempting to resend verification email for:', email);
    const result = await nhost.auth.signIn({ email });
    
    console.log('📥 Resend result:', result);
    
    if (result.error) {
      console.error('❌ Resend failed:', result.error);
    } else {
      console.log('✅ Resend successful');
    }
    
    return result;
  } catch (error) {
    console.error('💥 Exception during resend:', error);
    return { error };
  } finally {
    console.groupEnd();
  }
};

/**
 * Clear all authentication data
 */
export const clearAuthData = () => {
  console.log('🧹 Clearing all authentication data');
  
  // Clear Nhost session
  nhost.auth.signOut();
  
  // Clear stored emails
  localStorage.removeItem('pendingVerificationEmail');
  sessionStorage.removeItem('registrationEmail');
  sessionStorage.removeItem('pendingVerificationEmail');
  localStorage.removeItem('rememberedEmail');
  localStorage.removeItem('postVerificationRedirect');
  
  // Clear any other auth-related data
  localStorage.removeItem('nhostAccessToken');
  localStorage.removeItem('nhostRefreshToken');
  localStorage.removeItem('nhostSession');
  
  console.log('✅ Authentication data cleared');
};

// Make debug functions available globally in development
if (import.meta.env.DEV) {
  window.debugAuth = debugAuthState;
  window.testVerificationFlow = testVerificationFlow;
  window.clearAuthData = clearAuthData;
  
  console.log('🛠️ Debug functions available globally:');
  console.log('- window.debugAuth() - Show current auth state');
  console.log('- window.testVerificationFlow(email) - Test verification email sending');
  console.log('- window.clearAuthData() - Clear all auth data');
}
