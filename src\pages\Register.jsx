// src/pages/Register.jsx
import React, { useState, useEffect } from 'react';
import {
  Container, Typo<PERSON>, TextField, Button, Box, Alert, Link as MuiLink,
  CircularProgress, InputAdornment, IconButton, Checkbox, FormControlLabel,
  Divider, LinearProgress
} from '@mui/material';
import { useSignUpEmailPassword } from '@nhost/react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import PersonIcon from '@mui/icons-material/Person';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import {
  validateRegistrationForm,
  validatePassword,
  getPasswordStrengthColor,
  getPasswordStrengthLabel
} from '../utils/validation';

function Register() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [username, setUsername] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [error, setError] = useState('');
  const [formErrors, setFormErrors] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    terms: ''
  });
  const [formTouched, setFormTouched] = useState({
    username: false,
    email: false,
    password: false,
    confirmPassword: false,
    terms: false
  });
  const [passwordStrength, setPasswordStrength] = useState(0);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  // Use Nhost registration hook
  const {
    signUpEmailPassword,
    isLoading: nhostLoading,
    isSuccess,
    error: nhostError,
    needsEmailVerification
  } = useSignUpEmailPassword();

  // Calculate password strength whenever password changes
  useEffect(() => {
    if (!password) {
      setPasswordStrength(0);
      return;
    }

    const passwordValidation = validatePassword(password);
    setPasswordStrength(passwordValidation.strength || 0);
  }, [password]);

  // Validate form fields
  const validateField = (name, value) => {
    let errorMessage = '';

    switch (name) {
      case 'username':
        if (!value.trim()) {
          errorMessage = 'Username is required';
        } else if (value.trim().length < 3) {
          errorMessage = 'Username must be at least 3 characters';
        }
        break;

      case 'email':
        if (!value.trim()) {
          errorMessage = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(value)) {
          errorMessage = 'Please enter a valid email address';
        }
        break;

      case 'password':
        if (!value) {
          errorMessage = 'Password is required';
        } else if (value.length < 6) {
          errorMessage = 'Password must be at least 6 characters';
        } else if (passwordStrength < 30) {
          errorMessage = 'Password is too weak';
        }
        break;

      case 'confirmPassword':
        if (!value) {
          errorMessage = 'Please confirm your password';
        } else if (value !== password) {
          errorMessage = 'Passwords do not match';
        }
        break;

      case 'terms':
        if (!value) {
          errorMessage = 'You must accept the terms and conditions';
        }
        break;

      default:
        break;
    }

    return errorMessage;
  };

  // Handle field change with validation
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Update state based on input type
    if (type === 'checkbox') {
      if (name === 'acceptTerms') {
        setAcceptTerms(checked);
      }
      // Mark field as touched
      setFormTouched(prev => ({ ...prev, terms: true }));
      // Validate and set error
      const errorMessage = validateField('terms', checked);
      setFormErrors(prev => ({ ...prev, terms: errorMessage }));
    } else {
      // Handle text inputs
      if (name === 'username') {
        setUsername(value);
      } else if (name === 'email') {
        setEmail(value);
      } else if (name === 'password') {
        setPassword(value);
      } else if (name === 'confirmPassword') {
        setConfirmPassword(value);
      }

      // Mark field as touched
      setFormTouched(prev => ({ ...prev, [name]: true }));

      // Validate and set error
      const errorMessage = validateField(name, value);
      setFormErrors(prev => ({ ...prev, [name]: errorMessage }));

      // Special case: also validate confirmPassword when password changes
      if (name === 'password' && confirmPassword) {
        const confirmError = validateField('confirmPassword', confirmPassword);
        setFormErrors(prev => ({ ...prev, confirmPassword: confirmError }));
      }
    }
  };

  // Handle field blur for validation
  const handleBlur = (e) => {
    const { name, value, type, checked } = e.target;

    // Mark field as touched
    setFormTouched(prev => ({ ...prev, [name]: true }));

    // Validate based on input type
    if (type === 'checkbox') {
      const errorMessage = validateField('terms', checked);
      setFormErrors(prev => ({ ...prev, terms: errorMessage }));
    } else {
      const errorMessage = validateField(name, value);
      setFormErrors(prev => ({ ...prev, [name]: errorMessage }));
    }
  };

  // Handle registration success and errors using Nhost hook effects
  useEffect(() => {
    if (isSuccess) {
      console.log('Registration successful, checking verification status');

      // Store email for verification page (multiple storage methods for reliability)
      localStorage.setItem('pendingVerificationEmail', email.trim());
      sessionStorage.setItem('registrationEmail', email.trim());
      sessionStorage.setItem('pendingVerificationEmail', email.trim());

      // Show success message
      enqueueSnackbar('✅ Registration successful! Please verify your email to continue.', {
        variant: 'success',
        autoHideDuration: 5000,
        anchorOrigin: { vertical: 'top', horizontal: 'center' }
      });

      // Always redirect to verify-email page after registration
      // Most Nhost setups require email verification by default
      console.log('Redirecting to verification page after registration');
      navigate('/verify-email', { replace: true });
    }
  }, [isSuccess, email, navigate, enqueueSnackbar]);

  // Handle registration errors
  useEffect(() => {
    if (nhostError) {
      console.error("Nhost Registration failed:", nhostError);

      // Map Nhost errors to user-friendly messages
      let errorMessage = 'Registration failed. Please try again.';
      let fieldErrors = {};

      if (nhostError.message?.includes('email-already-in-use') ||
          nhostError.message?.includes('already exists')) {
        errorMessage = 'This email address is already registered. Try logging in instead.';
        fieldErrors.email = 'Email already in use';
      } else if (nhostError.message?.includes('password-too-short') ||
                 nhostError.message?.includes('weak-password')) {
        errorMessage = 'Password is too weak. Please choose a stronger password.';
        fieldErrors.password = 'Password is too weak';
      } else if (nhostError.message?.includes('invalid-email')) {
        errorMessage = 'Please enter a valid email address.';
        fieldErrors.email = 'Invalid email format';
      } else if (nhostError.message) {
        errorMessage = nhostError.message;
      }

      // Update form errors
      setFormErrors(prev => ({ ...prev, ...fieldErrors }));

      // Show error toast
      enqueueSnackbar(`❌ ${errorMessage}`, {
        variant: 'error',
        autoHideDuration: 6000,
        anchorOrigin: { vertical: 'top', horizontal: 'center' }
      });

      setError(errorMessage);
    }
  }, [nhostError, enqueueSnackbar]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    // Validate all fields using the validation utility
    const formValidation = validateRegistrationForm({
      username,
      email,
      password,
      confirmPassword,
      acceptTerms
    });

    // Update all form errors
    setFormErrors(formValidation.errors);

    // Mark all fields as touched
    setFormTouched({
      username: true,
      email: true,
      password: true,
      confirmPassword: true,
      terms: true
    });

    // Check if there are any validation errors
    if (!formValidation.isValid) {
      setError('Please correct the errors before registering.');
      return;
    }

    // Use Nhost hook to register user
    await signUpEmailPassword(email.trim(), password, {
      displayName: username.trim(),
      defaultRole: 'user',
      allowedRoles: ['user']
    });
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Toggle confirm password visibility
  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <Container component="main" maxWidth="xs" sx={{ mt: { xs: 4, sm: 8 }, mb: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2, border: theme => `1px solid ${theme.palette.divider}`, boxShadow: 1 }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography component="h1" variant="h4" align="center" gutterBottom sx={{ mb: 3 }}>
          Create Account
        </Typography>

        {/* Form-level error alert */}
        {error && (
          <Alert
            severity="error"
            sx={{ width: '100%', mb: 2 }}
            onClose={() => setError('')}
            icon={<ErrorOutlineIcon />}
          >
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit} noValidate sx={{ width: '100%' }}>
          {/* Username Field */}
          <TextField
            margin="normal"
            required
            fullWidth
            id="username"
            label="Username"
            name="username"
            autoComplete="username"
            autoFocus
            value={username}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={nhostLoading}
            error={formTouched.username && Boolean(formErrors.username)}
            helperText={formTouched.username && formErrors.username}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <PersonIcon color="action" />
                </InputAdornment>
              ),
            }}
            aria-label="Username"
          />

          {/* Email Field */}
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email Address"
            name="email"
            type="email"
            autoComplete="email"
            value={email}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={nhostLoading}
            error={formTouched.email && Boolean(formErrors.email)}
            helperText={formTouched.email && formErrors.email}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <EmailIcon color="action" />
                </InputAdornment>
              ),
            }}
            aria-label="Email address"
          />

          {/* Password Field */}
          <TextField
            margin="normal"
            required
            fullWidth
            name="password"
            label="Password (min. 6 characters)"
            type={showPassword ? 'text' : 'password'}
            id="password"
            autoComplete="new-password"
            value={password}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={nhostLoading}
            error={formTouched.password && Boolean(formErrors.password)}
            helperText={formTouched.password && formErrors.password}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LockIcon color="action" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={togglePasswordVisibility}
                    edge="end"
                    size="small"
                  >
                    {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            aria-label="Password"
          />

          {/* Password Strength Indicator */}
          {password && (
            <Box sx={{ mt: 1, mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="caption" color="text.secondary">
                  Password Strength:
                </Typography>
                <Typography
                  variant="caption"
                  color={getPasswordStrengthColor()}
                  sx={{ fontWeight: 'medium' }}
                >
                  {getPasswordStrengthLabel()}
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={passwordStrength}
                color={getPasswordStrengthColor()}
                sx={{ height: 6, borderRadius: 1 }}
              />
            </Box>
          )}

          {/* Confirm Password Field */}
          <TextField
            margin="normal"
            required
            fullWidth
            name="confirmPassword"
            label="Confirm Password"
            type={showConfirmPassword ? 'text' : 'password'}
            id="confirmPassword"
            autoComplete="new-password"
            value={confirmPassword}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={nhostLoading}
            error={formTouched.confirmPassword && Boolean(formErrors.confirmPassword)}
            helperText={formTouched.confirmPassword && formErrors.confirmPassword}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LockIcon color="action" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle confirm password visibility"
                    onClick={toggleConfirmPasswordVisibility}
                    edge="end"
                    size="small"
                  >
                    {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            aria-label="Confirm password"
          />

          {/* Terms and Conditions Checkbox */}
          <FormControlLabel
            control={
              <Checkbox
                name="acceptTerms"
                checked={acceptTerms}
                onChange={handleChange}
                onBlur={handleBlur}
                color="primary"
                size="small"
              />
            }
            label={
              <Typography variant="body2">
                I agree to the{' '}
                <MuiLink component={RouterLink} to="/terms" variant="body2">
                  Terms and Conditions
                </MuiLink>
              </Typography>
            }
            sx={{ mt: 2 }}
          />
          {formTouched.terms && formErrors.terms && (
            <Typography variant="caption" color="error" sx={{ ml: 2 }}>
              {formErrors.terms}
            </Typography>
          )}

          {/* Register Button */}
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2, py: 1.5 }}
            disabled={nhostLoading}
            aria-label="Create account button"
          >
            {nhostLoading ? <CircularProgress size={24} color="inherit" /> : 'Create Account'}
          </Button>

          <Divider sx={{ my: 2 }}>
            <Typography variant="body2" color="text.secondary">OR</Typography>
          </Divider>

          {/* Login Link */}
          <Typography variant="body2" align="center">
            Already have an account?{' '}
            <MuiLink component={RouterLink} to="/login" variant="body2">
              Login here
            </MuiLink>
          </Typography>
        </Box>
      </Box>
    </Container>
  );
}

export default Register;
