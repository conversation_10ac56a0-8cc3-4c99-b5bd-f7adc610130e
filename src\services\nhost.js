// src/services/nhost.js
import { NhostClient } from '@nhost/react';

// --- Configuration for Production ---
const subdomain = import.meta.env.VITE_NHOST_SUBDOMAIN;
const region = import.meta.env.VITE_NHOST_REGION;

// Determine if running in development (using <PERSON>ite's mode)
const isDev = import.meta.env.DEV;

console.log(`Nhost client initializing in ${isDev ? 'development' : 'production'} mode.`);
console.log(`Using Nhost subdomain: ${subdomain}, region: ${region}`);

// Constants for token refresh
const TOKEN_REFRESH_MARGIN = 10 * 60 * 1000; // 10 minutes in milliseconds
const REFRESH_RETRY_DELAY = 3000; // 3 seconds
const MAX_REFRESH_RETRIES = 2;

// Track if we're in the initial page load to prevent unnecessary token refreshes
const isInitialPageLoad = typeof window !== 'undefined' && document.readyState !== 'complete';

// Create a custom storage implementation
const createCustomStorage = () => {
  // Keep track of token refresh attempts to prevent excessive retries
  let refreshAttempts = 0;
  let lastRefreshTimestamp = 0;

  // Check if a token is valid and not expired
  const isTokenValid = (tokenData) => {
    if (!tokenData) return false;

    try {
      // For wrapped tokens with metadata
      if (typeof tokenData === 'string') {
        try {
          const parsed = JSON.parse(tokenData);
          if (parsed.expiresAt && parsed.expiresAt < Date.now()) {
            return false;
          }
          return !!parsed.value;
        } catch (e) {
          // Plain token string
          return !!tokenData;
        }
      }
      return !!tokenData;
    } catch (e) {
      return false;
    }
  };

  return {
    // Enhanced storage implementation with better error handling and token management
    setItem: (key, value) => {
      try {
        if (!value) {
          localStorage.removeItem(key);
          return;
        }

        if (key === 'nhostRefreshToken' && value) {
          // Reset refresh attempts when setting a new refresh token
          refreshAttempts = 0;
          lastRefreshTimestamp = Date.now();

          // Store refresh token with metadata
          const wrapper = {
            value,
            timestamp: Date.now(),
            expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days default expiry
          };
          localStorage.setItem(key, JSON.stringify(wrapper));
          console.debug('[Nhost] Refresh token stored with timestamp');
        } else if (key === 'nhostAccessToken' && value) {
          // Store access token with metadata
          const wrapper = {
            value,
            timestamp: Date.now(),
            // Typical JWT access tokens expire in 15 minutes
            expiresAt: Date.now() + (15 * 60 * 1000)
          };
          localStorage.setItem(key, JSON.stringify(wrapper));
          console.debug('[Nhost] Access token stored');
        } else {
          localStorage.setItem(key, value);
        }
      } catch (error) {
        console.error('[Nhost] Error storing auth token:', error);
      }
    },
    getItem: (key) => {
      try {
        // During initial page load, don't trigger unnecessary token refreshes
        if (isInitialPageLoad && key === 'nhostRefreshToken') {
          const now = Date.now();
          // If we've attempted a refresh recently, don't try again
          if (now - lastRefreshTimestamp < 5000) {
            refreshAttempts++;
            if (refreshAttempts > 1) {
              console.debug('[Nhost] Skipping excessive token refresh during page load');
              return null;
            }
          }
        }

        const item = localStorage.getItem(key);
        if (!item) return null;

        if (key === 'nhostRefreshToken' || key === 'nhostAccessToken') {
          try {
            const parsed = JSON.parse(item);

            // Check if token is expired
            if (parsed.expiresAt && parsed.expiresAt < Date.now()) {
              console.debug(`[Nhost] ${key} is expired, returning null`);
              return null;
            }

            // Return the actual token value
            return parsed.value;
          } catch (e) {
            // For backward compatibility with tokens stored without wrapper
            return item;
          }
        }
        return item;
      } catch (error) {
        console.error('[Nhost] Error retrieving auth token:', error);
        return null;
      }
    },
    removeItem: (key) => {
      try {
        localStorage.removeItem(key);
        if (key === 'nhostRefreshToken') {
          // Also clear related tokens to ensure complete logout
          localStorage.removeItem('nhostAccessToken');
          localStorage.removeItem('nhostSession');
        }
      } catch (error) {
        console.error('[Nhost] Error removing auth token:', error);
      }
    }
  };
};

// Initialize the Nhost client with optimized config for production
const nhost = new NhostClient({
  subdomain,
  region,
  clientStorageType: 'custom',
  autoRefreshToken: true,
  refreshIntervalTime: TOKEN_REFRESH_MARGIN,
  clientStorage: createCustomStorage(),
  start: true,
  // Add additional configuration for better session handling
  autoSignIn: true,
  devTools: isDev
});

// Set up global error handling for token refresh
let refreshInProgress = false;
let refreshQueue = [];
let lastRefreshTime = 0;
const MIN_REFRESH_INTERVAL = 10000; // Minimum 10 seconds between refresh attempts

/**
 * Safely refresh the authentication token with retry logic and rate limiting
 * @param {number} retryCount - Current retry attempt count
 * @param {boolean} force - Force refresh even if recently refreshed
 * @returns {Promise<{session: object|null, error: object|null}>} Result with session or error
 */
export const safeRefreshToken = async (retryCount = 0, force = false) => {
  // Check if we're already refreshing
  if (refreshInProgress) {
    console.debug('[Nhost] Token refresh already in progress, queuing request');
    return new Promise((resolve) => refreshQueue.push(resolve));
  }

  // Rate limiting to prevent excessive refresh attempts
  const now = Date.now();
  if (!force && now - lastRefreshTime < MIN_REFRESH_INTERVAL) {
    console.debug('[Nhost] Skipping refresh due to rate limiting');
    return {
      session: nhost.auth.getSession(),
      error: null
    };
  }

  // Check if we have a session before attempting refresh
  const currentSession = nhost.auth.getSession();
  if (currentSession?.accessToken && !force) {
    console.debug('[Nhost] Valid session exists, no need to refresh');
    return { session: currentSession, error: null };
  }

  refreshInProgress = true;
  lastRefreshTime = now;

  try {
    console.debug('[Nhost] Attempting to refresh token...');
    const result = await nhost.auth.refreshSession();
    refreshInProgress = false;

    if (result.error) {
      console.error('[Nhost] Token refresh failed with error:', result.error);

      // Only retry for specific error types that might be temporary
      const shouldRetry =
        retryCount < MAX_REFRESH_RETRIES &&
        (result.error.status === 500 || result.error.status === 429 || !result.error.status);

      if (shouldRetry) {
        console.debug(`[Nhost] Retrying token refresh (attempt ${retryCount + 1}/${MAX_REFRESH_RETRIES})`);
        await new Promise(resolve => setTimeout(resolve, REFRESH_RETRY_DELAY));
        return safeRefreshToken(retryCount + 1, true);
      }

      // If all retries fail or we shouldn't retry, clear the queue
      console.debug('[Nhost] Token refresh failed after retries or non-retriable error');
      refreshQueue.forEach(resolve => resolve({ session: null, error: result.error }));
      refreshQueue = [];

      // For 401 errors, clear the session to force re-login
      if (result.error.status === 401) {
        console.debug('[Nhost] Unauthorized error, clearing session');
        await nhost.auth.signOut();
      }

      return { session: null, error: result.error };
    }

    console.debug('[Nhost] Token refresh successful');
    refreshQueue.forEach(resolve => resolve(result));
    refreshQueue = [];
    return result;
  } catch (error) {
    console.error('[Nhost] Token refresh exception:', error);
    refreshInProgress = false;

    if (retryCount < MAX_REFRESH_RETRIES) {
      console.debug(`[Nhost] Retrying after exception (attempt ${retryCount + 1}/${MAX_REFRESH_RETRIES})`);
      await new Promise(resolve => setTimeout(resolve, REFRESH_RETRY_DELAY));
      return safeRefreshToken(retryCount + 1, true);
    }

    console.debug('[Nhost] Token refresh failed after exception retries');
    refreshQueue.forEach(resolve => resolve({ session: null, error }));
    refreshQueue = [];
    return { session: null, error };
  }
};

/**
 * Initialize session recovery on app start
 * This ensures that if a user refreshes the page, their session is properly restored
 */
const initializeSessionRecovery = async () => {
  try {
    console.debug('[Nhost] Initializing session recovery...');

    // Check if we have stored tokens
    const refreshTokenData = localStorage.getItem('nhostRefreshToken');
    if (!refreshTokenData) {
      console.debug('[Nhost] No refresh token found, user needs to sign in');
      return;
    }

    try {
      const parsed = JSON.parse(refreshTokenData);

      // Check if token is expired
      if (parsed.expiresAt && Date.now() > parsed.expiresAt) {
        console.debug('[Nhost] Refresh token expired, clearing storage');
        localStorage.removeItem('nhostRefreshToken');
        localStorage.removeItem('nhostAccessToken');
        localStorage.removeItem('nhostSession');
        return;
      }
    } catch (e) {
      // Handle legacy token format - just continue with the refresh attempt
      console.debug('[Nhost] Using legacy token format');
    }

    // Check current session status
    const currentSession = nhost.auth.getSession();
    if (currentSession?.accessToken) {
      console.debug('[Nhost] Valid session already exists');
      return;
    }

    // Attempt to refresh the session
    console.debug('[Nhost] Attempting to restore session with refresh token');
    const result = await safeRefreshToken(0, true);

    if (result.session) {
      console.debug('[Nhost] Session successfully restored');
    } else {
      console.debug('[Nhost] Failed to restore session:', result.error);
      // Clear invalid tokens
      localStorage.removeItem('nhostRefreshToken');
      localStorage.removeItem('nhostAccessToken');
      localStorage.removeItem('nhostSession');
    }
  } catch (error) {
    console.error('[Nhost] Error during session recovery:', error);
  }
};

// Initialize session recovery when the module loads
if (typeof window !== 'undefined') {
  // Use setTimeout to ensure this runs after the initial render
  setTimeout(initializeSessionRecovery, 100);
}

// Export the nhost client
export default nhost;

// Log the Nhost configuration for verification
console.log(`Nhost Configuration:`, {
  subdomain: nhost.auth.client.subdomain,
  region: nhost.auth.client.region,
  autoRefreshToken: nhost.auth.client.autoRefreshToken,
  clientStorageType: nhost.auth.client.clientStorageType
});

export { nhost, initializeSessionRecovery };
