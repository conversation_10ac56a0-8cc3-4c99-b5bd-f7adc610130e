import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import viteCompression from 'vite-plugin-compression';

export default defineConfig(({ mode }) => {
  // Load env variables for the current mode (development/production)
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [
      react(),
      viteCompression({ algorithm: 'gzip', threshold: 10240 }), // Compress assets >10kb
    ],
    base: mode === 'production' ? '/' : '/', // Set if deploying to a sub-path
    build: {
      outDir: 'dist',
      sourcemap: mode !== 'production', // Disable source maps in prod for security
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          entryFileNames: 'assets/[name].[hash].js',
          chunkFileNames: 'assets/[name].[hash].js',
          assetFileNames: 'assets/[name].[hash].[ext]',
        },
      },
    },
    
  };
});