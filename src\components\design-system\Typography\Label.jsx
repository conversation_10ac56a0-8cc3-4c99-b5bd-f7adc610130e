// src/components/design-system/Typography/Label.jsx
import React from 'react';
import { Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledLabel = styled(Typography)(({ theme, size, weight, required, disabled }) => ({
  fontWeight: weight || theme.typography.fontWeight?.medium || 500,
  color: disabled ? theme.palette.text.disabled : theme.palette.text.primary,
  marginBottom: theme.spacing(0.5),
  display: 'block',
  
  // Size variants
  ...(size === 'sm' && {
    fontSize: theme.typography.fontSize?.sm || '0.875rem',
  }),
  ...(size === 'base' && {
    fontSize: theme.typography.fontSize?.base || '1rem',
  }),
  
  // Required indicator
  ...(required && {
    '&::after': {
      content: '" *"',
      color: theme.palette.error.main,
    },
  }),
}));

/**
 * Label component for form labels and descriptive text
 * 
 * @param {Object} props
 * @param {'sm'|'base'} props.size - Label size
 * @param {number} props.weight - Font weight
 * @param {boolean} props.required - Whether to show required indicator
 * @param {boolean} props.disabled - Whether the label is disabled
 * @param {string} props.htmlFor - Associated form element ID
 * @param {React.ReactNode} props.children - Label text
 */
const Label = ({ 
  size = 'base',
  weight,
  required = false,
  disabled = false,
  htmlFor,
  children,
  ...props 
}) => {
  return (
    <StyledLabel
      component="label"
      htmlFor={htmlFor}
      size={size}
      weight={weight}
      required={required}
      disabled={disabled}
      {...props}
    >
      {children}
    </StyledLabel>
  );
};

export default Label;
