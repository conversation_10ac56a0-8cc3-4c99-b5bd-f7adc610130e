import { useEffect } from 'react';
import { nhost, safeRefreshToken } from '../services/nhost'; // Corrected import path

/**
 * Nhost Auth Error Handler Hook
 *
 * This hook listens for Nhost authentication state changes.
 * If the session becomes null (e.g., due to an invalid token, explicit logout,
 * or a failed token refresh that results in the SDK invalidating the session),
 * it ensures the user is fully signed out and Nhost's local storage is cleared.
 *
 * This helps address scenarios where a token might become invalid (e.g., 401 on refresh)
 * and ensures the application state reflects that the user is no longer authenticated.
 */
function useNhostAuthErrorHandler() {
  useEffect(() => {
    console.log('[AuthErrorHandler] Hook initialized, subscribing to auth state changes.');

    const handleAuthStateChange = async (event, session) => {
      console.log(`[AuthErrorHandler] Auth state changed. Event: ${event}, Session:`, session);

      // If the session is null, it means the user is effectively signed out or the token is invalid.
      // Nhost's onAuthStateChanged fires with a null session in such cases.
      if (session === null) {
        console.log('[AuthErrorHandler] Session is null. Checking if we can recover...');

        // Try to recover the session before signing out
        try {
          const refreshResult = await safeRefreshToken();
          if (refreshResult.session) {
            console.log('[AuthErrorHandler] Session recovered successfully');
            return;
          }
        } catch (error) {
          console.warn('[AuthErrorHandler] Failed to recover session:', error);
        }

        console.log('[AuthErrorHandler] Unable to recover session. Ensuring user is signed out and local Nhost storage is cleared.');
        // Calling signOut() ensures that Nhost clears its stored session data (e.g., in localStorage)
        // and performs any other necessary cleanup.
        // This is particularly important if the session became null due to an error (like a failed token refresh).
        nhost.auth.signOut().catch(error => {
          console.error('[AuthErrorHandler] Error during explicit signOut on null session:', error);
        });
        // Note: Explicitly removing 'nhostRefreshToken' and 'nhostSession' from localStorage
        // is generally not needed if nhost.auth.signOut() is called and clientStorageType is 'localStorage',
        // as Nhost's signOut method should handle this.
        // However, if issues persist, they could be re-added as a fallback.
        // localStorage.removeItem('nhostRefreshToken');
        // localStorage.removeItem('nhostSession');
      }
    };

    const unsubscribeAuthStateChanged = nhost.auth.onAuthStateChanged(handleAuthStateChange);

    const handleTokenChange = async (session) => {
      console.log('[AuthErrorHandler] Token changed. Session:', session);
      // If the session becomes null after a token change (e.g., refresh failed),
      // it indicates the token is now invalid.
      if (session === null) {
        console.log('[AuthErrorHandler] Session is null (onTokenChanged). Attempting recovery...');

        // Try to recover the session
        try {
          const refreshResult = await safeRefreshToken();
          if (refreshResult.session) {
            console.log('[AuthErrorHandler] Session recovered after token change');
            return;
          }
        } catch (error) {
          console.warn('[AuthErrorHandler] Failed to recover session after token change:', error);
        }

        console.log('[AuthErrorHandler] Unable to recover. Ensuring user is signed out.');
        nhost.auth.signOut().catch(error => {
          console.error('[AuthErrorHandler] Error during explicit signOut on null session (onTokenChanged):', error);
        });
      }
    };

    const { unsubscribe: unsubscribeTokenChanged } = nhost.auth.onTokenChanged(handleTokenChange);

    const handleAuthError = (error) => {
      console.error('[AuthErrorHandler] Nhost Auth Error Event:', error);
      // Check for errors that typically indicate an invalid/expired refresh token or session
      // Nhost errors might have a `status` property or specific messages.
      const status = error?.response?.status || error?.status;
      const message = error?.message?.toLowerCase() || '';

      if (status === 401 || message.includes('invalid token') || message.includes('unauthorized') || message.includes('invalid_grant')) {
        console.log('[AuthErrorHandler] Detected critical auth error (e.g., 401, invalid_grant). Ensuring user is signed out.');
        nhost.auth.signOut().catch(err => {
          console.error('[AuthErrorHandler] Error during signOut triggered by onAuthError:', err);
        });
      }
    };

    const unsubscribeAuthError = nhost.auth.onAuthError(handleAuthError);

    return () => {
      console.log('[AuthErrorHandler] Hook cleanup. Unsubscribing from auth state, token, and auth error changes.');
      unsubscribeAuthStateChanged();
      unsubscribeTokenChanged();
      unsubscribeAuthError();
    };
  }, []); // Empty dependency array ensures this runs once on mount and cleans up on unmount
}

export default useNhostAuthErrorHandler;
