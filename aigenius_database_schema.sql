-- AIGenius Space Complete Database Schema
-- Execute these statements in order

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ========================================
-- CORE TABLES
-- ========================================

-- 1. Pass Types (Subscription Plans)
CREATE TABLE pass_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    tier TEXT NOT NULL CHECK (tier IN ('Free', 'Paid', 'Lite', 'Pro', 'Elite')),
    category TEXT NOT NULL CHECK (category IN ('Visitor', 'Citizen')),
    monthly_price DECIMAL(10,2),
    hours_included INTEGER,
    is_unlimited BOOLEAN DEFAULT false,
    slow_generation_limit INTEGER,
    fast_generation_limit INTEGER,
    provider_price_id TEXT,
    features JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. User Passes (Active Subscriptions)
CREATE TABLE user_passes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    pass_type_id UUID NOT NULL REFERENCES pass_types(id) ON DELETE RESTRICT,
    start_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    end_date TIMESTAMPTZ,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    hours_used INTEGER DEFAULT 0,
    provider_subscription_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Add foreign key constraint (will be added after auth setup)
    CONSTRAINT fk_user_passes_user_id FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE

    -- Add server access tracking
ALTER TABLE user_passes 
ADD COLUMN allowed_server_types TEXT[] DEFAULT ARRAY['slow'],
ADD COLUMN auto_downgraded_from UUID REFERENCES pass_types(id);

);

-- 3. User Wallet (Financial Tracking)
CREATE TABLE user_wallet (
    user_id UUID PRIMARY KEY,
    credits_balance INTEGER DEFAULT 0,
    fast_credits_balance INTEGER DEFAULT 0,
    last_free_credit_grant TIMESTAMPTZ,
    active_pass_id UUID REFERENCES user_passes(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Add foreign key constraint (will be added after auth setup)
    CONSTRAINT fk_user_wallet_user_id FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE

    -- Remove fast_credits_balance, keep only credits_balance
-- Add new fields for tracking
ALTER TABLE user_wallet 
DROP COLUMN fast_credits_balance,
ADD COLUMN last_free_credit_claim TIMESTAMPTZ,
ADD COLUMN daily_free_credits_granted INTEGER DEFAULT 0,
ADD COLUMN total_credits_purchased INTEGER DEFAULT 0;

);

-- 4. Credit Bundles (One-time Purchases)
CREATE TABLE credit_bundles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    credits_amount INTEGER NOT NULL,
    is_featured BOOLEAN DEFAULT false,
    provider_price_id TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Credit Transactions (Audit Trail)
CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    change_amount INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    fast_balance_after INTEGER,
    deducted_from TEXT CHECK (deducted_from IN ('credits', 'fast_credits', 'overdraft')),
    reason TEXT NOT NULL,
    related_id TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Add foreign key constraint (will be added after auth setup)
    CONSTRAINT fk_credit_transactions_user_id FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE

    -- Remove fast_balance_after and deducted_from columns
-- Add new tracking fields
ALTER TABLE credit_transactions 
DROP COLUMN fast_balance_after,
DROP COLUMN deducted_from,
ADD COLUMN transaction_type TEXT CHECK (transaction_type IN ('deduction', 'purchase', 'free_claim', 'refund')),
ADD COLUMN server_type TEXT CHECK (server_type IN ('slow', 'fast')),
ADD COLUMN seconds_consumed INTEGER,
ADD COLUMN cost_per_second DECIMAL(10,4);

);

-- 6. Tools (Available AI Tools)
CREATE TABLE tools (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    workflow_file_id UUID,
    slow_credit_cost_per_second DECIMAL(10,4) DEFAULT 0.1,
    fast_credit_cost_per_second DECIMAL(10,4) DEFAULT 0.5,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Add foreign key constraint for storage files
    CONSTRAINT fk_tools_workflow_file_id FOREIGN KEY (workflow_file_id) REFERENCES storage.files(id) ON DELETE SET NULL
);

-- 7. ComfyUI Servers (Processing Servers)
CREATE TABLE comfyui_servers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL,
    server_type TEXT NOT NULL CHECK (server_type IN ('slow', 'fast')),
    is_active BOOLEAN DEFAULT true,
    tool_id TEXT,
    queue_priority INTEGER DEFAULT 0,
    cost_credits_per_second DECIMAL(10,4),
    max_concurrent_jobs INTEGER DEFAULT 1,
    current_jobs INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Add foreign key constraint
    CONSTRAINT fk_comfyui_servers_tool_id FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE SET NULL

    -- Rename and clarify cost structure
ALTER TABLE comfyui_servers 
RENAME COLUMN cost_credits_per_second TO credits_per_second,
ADD COLUMN min_pass_tier TEXT CHECK (min_pass_tier IN ('Free', 'Paid', 'Lite', 'Pro', 'Elite')),
ADD COLUMN allowed_categories TEXT[] DEFAULT ARRAY['Visitor', 'Citizen'];

);

-- 8. Projects (Saved Generations)
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    prompt_id UUID,
    name TEXT,
    input_file_url TEXT,
    prompt_input_text TEXT,
    credit_cost DECIMAL(10,2),
    generation_duration_ms INTEGER,
    outputs JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    saved_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Add foreign key constraints (will be added after auth setup)
    CONSTRAINT fk_projects_user_id FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- 9. Prompts (Generation Attempts)
CREATE TABLE prompts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    tool_id TEXT NOT NULL,
    input_file TEXT,
    user_prompt TEXT,
    server_type TEXT NOT NULL CHECK (server_type IN ('slow', 'fast')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
    error_message TEXT,
    uploaded_file_id UUID,
    credit_cost INTEGER,
    generation_duration_ms INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Add foreign key constraints
    CONSTRAINT fk_prompts_user_id FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
    CONSTRAINT fk_prompts_tool_id FOREIGN KEY (tool_id) REFERENCES tools(id) ON DELETE RESTRICT,
    CONSTRAINT fk_prompts_uploaded_file_id FOREIGN KEY (uploaded_file_id) REFERENCES storage.files(id) ON DELETE SET NULL
);

-- Add the missing foreign key constraint for projects
ALTER TABLE projects 
ADD CONSTRAINT fk_projects_prompt_id FOREIGN KEY (prompt_id) REFERENCES prompts(id) ON DELETE SET NULL;

-- ========================================
-- INDEXES FOR PERFORMANCE
-- ========================================

-- User-based queries
CREATE INDEX idx_user_passes_user_id ON user_passes(user_id);
CREATE INDEX idx_user_passes_status ON user_passes(status);
CREATE INDEX idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_prompts_user_id ON prompts(user_id);

-- Status and filtering
CREATE INDEX idx_prompts_status ON prompts(status);
CREATE INDEX idx_prompts_created_at ON prompts(created_at);
CREATE INDEX idx_projects_created_at ON projects(created_at);
CREATE INDEX idx_pass_types_active ON pass_types(is_active);
CREATE INDEX idx_credit_bundles_active ON credit_bundles(is_active);

-- Server management
CREATE INDEX idx_comfyui_servers_active ON comfyui_servers(is_active);
CREATE INDEX idx_comfyui_servers_type ON comfyui_servers(server_type);

-- ========================================
-- TRIGGERS FOR UPDATED_AT
-- ========================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_pass_types_updated_at BEFORE UPDATE ON pass_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_passes_updated_at BEFORE UPDATE ON user_passes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_wallet_updated_at BEFORE UPDATE ON user_wallet FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_credit_bundles_updated_at BEFORE UPDATE ON credit_bundles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tools_updated_at BEFORE UPDATE ON tools FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_comfyui_servers_updated_at BEFORE UPDATE ON comfyui_servers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_prompts_updated_at BEFORE UPDATE ON prompts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- INITIAL DATA
-- ========================================

-- Insert default pass types
INSERT INTO pass_types (name, tier, category, monthly_price, hours_included, is_unlimited, slow_generation_limit, fast_generation_limit, features, sort_order) VALUES
('Visitor Pass: Free Tier', 'Free', 'Visitor', 0.00, 1, false, 5, 0, '{"daily_free_credits": 10}', 1),
('Visitor Pass: Paid Tier', 'Paid', 'Visitor', 0.00, NULL, true, NULL, NULL, '{"pay_per_use": true}', 2),
('Citizen Pass: Lite', 'Lite', 'Citizen', 9.99, 10, false, 50, 5, '{"priority_support": false}', 3),
('Citizen Pass: Pro', 'Pro', 'Citizen', 19.99, 25, false, 200, 25, '{"priority_support": true, "advanced_tools": true}', 4),
('Citizen Pass: Elite', 'Elite', 'Citizen', 39.99, NULL, true, NULL, NULL, '{"priority_support": true, "advanced_tools": true, "unlimited": true}', 5);

-- Insert default credit bundles
INSERT INTO credit_bundles (name, description, price, credits_amount, is_featured, sort_order) VALUES
('Starter Pack', '30 credits for quick tasks', 1.00, 30, false, 1),
('Popular Pack', '100 credits - Best Value!', 3.00, 100, true, 2),
('Power Pack', '300 credits for heavy users', 8.00, 300, false, 3),
('Mega Pack', '1000 credits - Maximum value', 25.00, 1000, false, 4);

-- ========================================
-- HELPER FUNCTIONS
-- ========================================

-- Function to create user wallet on user registration
CREATE OR REPLACE FUNCTION create_user_wallet()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_wallet (user_id, credits_balance, fast_credits_balance)
    VALUES (NEW.id, 10, 0); -- Start with 10 free credits
    
    -- Assign default free pass
    INSERT INTO user_passes (user_id, pass_type_id, status)
    SELECT NEW.id, id, 'active'
    FROM pass_types 
    WHERE name = 'Visitor Pass: Free Tier'
    LIMIT 1;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new user wallet creation
-- Note: This trigger should be created on auth.users table
-- CREATE TRIGGER on_user_created AFTER INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION create_user_wallet();