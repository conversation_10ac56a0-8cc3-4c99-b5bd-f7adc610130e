// src/components/design-system/Interactive/Input.jsx
import React from 'react';
import { TextField, FormControl, FormHelperText } from '@mui/material';
import { styled } from '@mui/material/styles';
import Label from '../Typography/Label';

const StyledFormControl = styled(FormControl)(({ theme, fullWidth }) => ({
  ...(fullWidth && {
    width: '100%',
  }),
}));

/**
 * Input component with consistent styling and validation
 * 
 * @param {Object} props
 * @param {string} props.label - Input label
 * @param {string} props.placeholder - Input placeholder
 * @param {string} props.value - Input value
 * @param {function} props.onChange - Change handler
 * @param {function} props.onBlur - Blur handler
 * @param {'text'|'email'|'password'|'number'|'tel'|'url'} props.type - Input type
 * @param {'small'|'medium'} props.size - Input size
 * @param {boolean} props.required - Whether input is required
 * @param {boolean} props.disabled - Whether input is disabled
 * @param {boolean} props.error - Whether input has error
 * @param {string} props.helperText - Helper text below input
 * @param {boolean} props.fullWidth - Whether input takes full width
 * @param {React.ReactNode} props.startAdornment - Content before input
 * @param {React.ReactNode} props.endAdornment - Content after input
 */
const Input = ({ 
  label,
  placeholder,
  value,
  onChange,
  onBlur,
  type = 'text',
  size = 'medium',
  required = false,
  disabled = false,
  error = false,
  helperText,
  fullWidth = true,
  startAdornment,
  endAdornment,
  ...props 
}) => {
  const inputId = `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <StyledFormControl fullWidth={fullWidth} error={error}>
      {label && (
        <Label 
          htmlFor={inputId}
          required={required}
          disabled={disabled}
          size={size === 'small' ? 'sm' : 'base'}
        >
          {label}
        </Label>
      )}
      <TextField
        id={inputId}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        type={type}
        size={size}
        required={required}
        disabled={disabled}
        error={error}
        fullWidth={fullWidth}
        InputProps={{
          startAdornment,
          endAdornment,
        }}
        {...props}
      />
      {helperText && (
        <FormHelperText>
          {helperText}
        </FormHelperText>
      )}
    </StyledFormControl>
  );
};

export default Input;
