{"name": "aigenius-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@apollo/client": "^3.13.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.0", "@mui/lab": "^7.0.0-beta.9", "@mui/material": "^7.0.0", "@mui/x-date-pickers": "^7.28.0", "@nhost/react": "^3.10.3", "@nhost/react-apollo": "^17.0.3", "@nhost/react-auth": "^3.0.0", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "dotenv": "^16.4.7", "graphql": "^16.10.0", "graphql-ws": "^6.0.4", "gsap": "^3.12.7", "notistack": "^3.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.30.0", "ws": "^8.18.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/express": "^5.0.2", "@types/jest": "^29.5.3", "@types/node": "^22.15.29", "@vitejs/plugin-react": "^4.3.4", "babel-jest": "^29.7.0", "express": "^5.1.0", "graphql-request": "^4.3.0", "jest": "^29.6.1", "jest-environment-jsdom": "^29.6.1", "msw": "^1.2.2", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "uuid": "^11.1.0", "vite": "^6.2.6", "vite-plugin-compression": "^0.5.1"}}