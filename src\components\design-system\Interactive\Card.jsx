// src/components/design-system/Interactive/Card.jsx
import React from 'react';
import { Card as <PERSON>i<PERSON>ard, CardContent, CardActions, CardHeader, CardMedia } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Mui<PERSON><PERSON>, {
  shouldForwardProp: (prop) => !['interactive'].includes(prop),
})(({ theme, variant, interactive }) => ({
  ...(variant === 'outlined' && {
    border: `2px solid ${theme.palette.divider}`,
  }),
  ...(variant === 'elevated' && {
    boxShadow: theme.shadows[4],
    border: 'none',
  }),
  ...(interactive && {
    cursor: 'pointer',
    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: theme.shadows[8],
    },
  }),
}));

/**
 * Card component for content containers
 *
 * @param {Object} props
 * @param {'default'|'outlined'|'elevated'} props.variant - Card variant
 * @param {boolean} props.interactive - Whether card is interactive/clickable
 * @param {function} props.onClick - Click handler for interactive cards
 * @param {React.ReactNode} props.children - Card content
 */
const Card = ({
  variant = 'default',
  interactive = false,
  onClick,
  children,
  ...props
}) => {
  return (
    <StyledCard
      variant={variant}
      interactive={interactive}
      onClick={onClick}
      {...props}
    >
      {children}
    </StyledCard>
  );
};

/**
 * CardHeader component for card titles and actions
 */
const CardHeaderComponent = ({ title, subheader, action, avatar, ...props }) => {
  return (
    <CardHeader
      title={title}
      subheader={subheader}
      action={action}
      avatar={avatar}
      {...props}
    />
  );
};

/**
 * CardContent component for main card content
 */
const CardContentComponent = ({ children, ...props }) => {
  return (
    <CardContent {...props}>
      {children}
    </CardContent>
  );
};

/**
 * CardActions component for card action buttons
 */
const CardActionsComponent = ({ children, ...props }) => {
  return (
    <CardActions {...props}>
      {children}
    </CardActions>
  );
};

/**
 * CardMedia component for card images/media
 */
const CardMediaComponent = ({ image, title, height = 200, ...props }) => {
  return (
    <CardMedia
      component="img"
      height={height}
      image={image}
      alt={title}
      {...props}
    />
  );
};

Card.Header = CardHeaderComponent;
Card.Content = CardContentComponent;
Card.Actions = CardActionsComponent;
Card.Media = CardMediaComponent;

export default Card;
