// src/components/ProtectedRoute.jsx
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthenticationStatus, useUserData } from '@nhost/react';
import { Box, CircularProgress } from '@mui/material';

/**
 * ProtectedRoute component following Nhost best practices
 * Handles authentication state and email verification requirements
 */
function ProtectedRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();
  const location = useLocation();

  console.log("[ProtectedRoute] Authentication State:", {
    path: location.pathname,
    isLoading,
    isAuthenticated,
    user: user ? { id: user.id, email: user.email, emailVerified: user.emailVerified } : null
  });

  // Show loading spinner while authentication status is being determined
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '80vh'
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    console.debug('[ProtectedRoute] User not authenticated, redirecting to login');
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Handle email verification requirement
  if (user && !user.emailVerified) {
    // Allow access to verification page
    if (location.pathname === '/verify-email') {
      console.debug('[ProtectedRoute] User on verification page, allowing access');
      return children;
    } else {
      // Redirect unverified users to verification page
      console.debug('[ProtectedRoute] User not verified, redirecting to verification page');
      console.debug('[ProtectedRoute] User verification status:', {
        userId: user.id,
        email: user.email,
        emailVerified: user.emailVerified,
        currentPath: location.pathname
      });

      // Store the intended destination for after verification
      localStorage.setItem('postVerificationRedirect', location.pathname);

      // Store user email for verification page
      if (user.email) {
        localStorage.setItem('pendingVerificationEmail', user.email);
        sessionStorage.setItem('pendingVerificationEmail', user.email);
      }

      return <Navigate to="/verify-email" replace />;
    }
  }

  // User is authenticated and verified
  if (user && user.emailVerified) {
    // If user is already verified but on verification page, redirect to intended destination
    if (location.pathname === '/verify-email') {
      const intendedDestination = localStorage.getItem('postVerificationRedirect') || '/dashboard';
      localStorage.removeItem('postVerificationRedirect');
      console.debug('[ProtectedRoute] User already verified, redirecting to:', intendedDestination);
      return <Navigate to={intendedDestination} replace />;
    }
  }

  // All checks passed, render the protected content
  return children;
}

/**
 * PublicOnlyRoute component for login/register pages
 * Redirects authenticated users to appropriate pages
 */
export function PublicOnlyRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const user = useUserData();

  console.log("[PublicOnlyRoute] Authentication State:", {
    isLoading,
    isAuthenticated,
    user: user ? { id: user.id, email: user.email, emailVerified: user.emailVerified } : null
  });

  // Show loading spinner while authentication status is being determined
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '80vh'
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // If user is authenticated, redirect to appropriate page
  if (isAuthenticated && user) {
    if (user.emailVerified) {
      console.debug('[PublicOnlyRoute] User authenticated and verified, redirecting to dashboard');
      return <Navigate to="/dashboard" replace />;
    } else {
      console.debug('[PublicOnlyRoute] User authenticated but not verified, redirecting to verification');
      return <Navigate to="/verify-email" replace />;
    }
  }

  // User is not authenticated, show the public route
  return children;
}

export default ProtectedRoute;
