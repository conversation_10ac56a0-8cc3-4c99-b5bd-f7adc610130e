// src/hooks/useResponsive.js
import { useState, useEffect } from 'react';

/**
 * Custom hook for responsive behavior
 * @param {number} breakpoint - The breakpoint in pixels (default: 768)
 * @returns {object} - Object containing responsive state and utilities
 */
export const useResponsive = (breakpoint = 768) => {
  const [isMobile, setIsMobile] = useState(window.innerWidth <= breakpoint);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setWindowWidth(width);
      setIsMobile(width <= breakpoint);
    };

    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Call handler right away so state gets updated with initial window size
    handleResize();
    
    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [breakpoint]);

  return {
    isMobile,
    isDesktop: !isMobile,
    windowWidth,
    breakpoint,
    // Utility functions
    isAbove: (width) => windowWidth > width,
    isBelow: (width) => windowWidth <= width,
    isBetween: (min, max) => windowWidth > min && windowWidth <= max,
  };
};

/**
 * Hook specifically for navbar responsive behavior
 * Uses the same breakpoint as the navbar component
 */
export const useNavbarResponsive = () => {
  return useResponsive(768);
};

export default useResponsive;
