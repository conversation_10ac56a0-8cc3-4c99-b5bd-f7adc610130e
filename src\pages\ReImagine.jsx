// src/pages/ReImagine.jsx
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
    CircularProgress, Grid, Box,
    Paper, Avatar, Snackbar, Alert as MuiAlert, Fade, Card, CardMedia, CardContent
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import ImageSearchIcon from '@mui/icons-material/ImageSearch';

// Design System Components
import {
    Container,
    Stack,
    Heading,
    Text,
    Button
} from '../components/design-system/index';

// Nhost Hooks
import { useAuthenticationStatus, useUserId } from '@nhost/react';

// Custom Services & Hooks
import { GenerationService } from '../services/generationService';
import { CreditService } from '../services/creditService';
import { useStopwatch } from '../hooks/useStopwatch';
import { useCreditWallet } from '../hooks/useCreditWallet';
import { useServerAccess } from '../hooks/useServerAccess';

// Components
import ServerAccessIndicator from '../components/ServerAccessIndicator';

// Page Specific Components
import AIGenerationLoader from '../components/AIGenerationLoader';
import AnimatedTextField from '../components/AnimatedTextField';

// --- MUI Alert component ---
const Alert = React.forwardRef(function Alert(props, ref) {
    return <MuiAlert {...props} ref={ref} variant="filled" elevation={6}/>;
});

// --- Configuration ---
const TOOL_ID = 'reimagine';
const INPUT_IMAGE_NODE_ID = '56';
const INPUT_PROMPT_NODE_ID = '6';
const PROMPT_INPUT_KEY = 'text';
const MAX_CACHE_SIZE_BYTES = 5 * 1024 * 1024; // 5MB limit for Base64 cache

// --- Helper Function for Local Caching ---
const cacheImageLocally = async (imageUrl, projectId) => {
    if (!imageUrl || !projectId || !(imageUrl.startsWith('http') || imageUrl.startsWith('blob:'))) {
        console.warn("[Cache] Skipping cache for invalid URL or project ID.", {imageUrl, projectId});
        return;
    }
    console.log(`[Cache] Attempting to cache image for project ${projectId} from ${imageUrl.substring(0, 100)}...`);
    try {
        const response = await fetch(imageUrl);
        if (!response.ok) throw new Error(`Failed to fetch image: ${response.statusText}`);
        const blob = await response.blob();
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onloadend = () => {
            const base64String = reader.result;
            if (typeof base64String === 'string') {
                if (base64String.length > MAX_CACHE_SIZE_BYTES) { console.warn(`[Cache] Image size exceeds limit. Skipping cache for project ${projectId}.`); return; }
                try { localStorage.setItem(`aigenius_output_${projectId}`, base64String); console.log(`[Cache] Successfully cached image for project ${projectId}.`); }
                catch (storageError) { console.error("[Cache] Error saving to localStorage:", storageError); }
            }
        };
        reader.onerror = (error) => { console.error("[Cache] Error converting Blob to Base64:", error); };
    } catch (error) { console.error("[Cache] Failed to fetch or cache image:", error); }
};



function ReImagine() {
    const theme = useTheme();
    const { isLoading: isLoadingAuth, isAuthenticated } = useAuthenticationStatus();
    const userId = useUserId();

    // --- State ---
    const [file, setFile] = useState(null);
    const [previewUrl, setPreviewUrl] = useState(null);
    const [prompt, setPrompt] = useState('');
    const [outputUrl, setOutputUrl] = useState('');
    const [loading, setLoading] = useState(false);
    const [outputObjectForSave, setOutputObjectForSave] = useState(null);
    const [saveInProgress, setSaveInProgress] = useState(false);
    const [error, setError] = useState(null);
    const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });
    const currentProjectIdRef = useRef(null);

    // --- Stopwatch Hook ---
    const {
        formattedTime, isRunning: isStopwatchRunning, finalTime,
        start: startStopwatch, stop: stopStopwatch, reset: resetStopwatch
    } = useStopwatch({ tickInterval: 1000 });

    // --- Handlers & Effects ---
    const handleCloseToast = useCallback(() => { setToast(prev => ({ ...prev, open: false })); }, []);
    const showToast = useCallback((message, severity = 'info') => { setToast({ open: true, message, severity }); }, []);

    useEffect(() => {
        let objectUrl = null;
        if (file) { objectUrl = URL.createObjectURL(file); setPreviewUrl(objectUrl); }
        else { setPreviewUrl(null); }
        return () => { if (objectUrl) URL.revokeObjectURL(objectUrl); };
    }, [file]);

    const handleFileChange = (e) => {
        const selectedFile = e.target.files?.[0] || null;
        setFile(selectedFile);
        setOutputUrl(''); setOutputObjectForSave(null); setError(null);
        resetStopwatch();
        setLoading(false); setSaveInProgress(false); currentProjectIdRef.current = null;
        if (selectedFile) showToast('Image selected. Ready.', 'info');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!isAuthenticated || !userId || !file || loading) return;
        setLoading(true); showToast('Initiating generation...', 'info');
        setOutputUrl(''); setOutputObjectForSave(null); setError(null);
        resetStopwatch();
        currentProjectIdRef.current = null;
        startStopwatch();
        let jobResult = null; let finalCost = 0;
        try {
            jobResult = await GenerationService.runGenerationJob(
                userId, TOOL_ID, file, prompt,
                INPUT_IMAGE_NODE_ID, INPUT_PROMPT_NODE_ID, PROMPT_INPUT_KEY
            );

            stopStopwatch();
            finalCost = jobResult.usageDetails.calculatedCost;
            console.log(`[ReImagine] Generation completed. Cost: ${finalCost} credits. PromptLogId: ${jobResult.promptLogId}`);

            // Get the first output URL from the generation results
            const firstOutput = jobResult.generationOutputs?.[0];
            if (!firstOutput?.url) {
                throw new Error('No output URL received from generation service');
            }

            setOutputUrl(firstOutput.url);

            // --- Prepare complete data for saving (local and potentially cloud) ---
            const finalOutputData = {
                promptId: jobResult.promptLogId, // Use the ID returned from logging
                outputUrl: firstOutput.url,
                toolId: TOOL_ID,
                userId,
                timestamp: new Date().toISOString(), // Use for local sorting
                originalFileName: file.name,
                promptInputText: prompt.trim() || 'Reimagine this image',
                outputs: jobResult.generationOutputs, // Use the new outputs structure
                creditCost: finalCost,
                generationDurationMs: Math.round(jobResult.usageDetails.durationMs),
                serverTypeUsed: jobResult.usageDetails.serverTypeUsed,
                // Add a flag to distinguish locally saved items
                status: 'local', // Indicate it's not saved to DB yet
            };

            setOutputObjectForSave(finalOutputData); // Keep for the immediate "Save" button
            showToast(`Output Ready! (${finalCost} credits used)`, 'success');

            // --- ADD: Save to Local Storage for Recovery ---
            if (finalOutputData.promptId) {
                try {
                    localStorage.setItem(`output-${finalOutputData.promptId}`, JSON.stringify(finalOutputData));
                    console.log(`[ReImagine] Saved generation details locally for promptId: ${finalOutputData.promptId}`);
                } catch (storageError) {
                    console.error("[ReImagine] Error saving to localStorage:", storageError);
                    // Optionally show a non-blocking warning toast
                }
            } else {
                 console.warn("[ReImagine] Cannot save to local storage without a promptId.");
            }
            // --- End ADD ---

        } catch (err) {
            stopStopwatch();
            console.error(`[ReImagine] Generation Failed:`, err);
            const message = (err instanceof Error) ? err.message : "An unknown error occurred.";
            const displayError = message.startsWith('ComfyUI Error:') ? 'Generation failed internally. Please try again.' : message;
            showToast(displayError, 'error');
            setError(displayError);
        } finally {
            setLoading(false);
        }
    };

    const handleSaveProject = async () => {
        if (!outputObjectForSave || saveInProgress || !outputUrl) return;
        setSaveInProgress(true);
        // Use the existing showToast for feedback
        showToast('Saving project...', 'info');
        try {
            const dataToSave = { ...outputObjectForSave };

            // Remove local-specific flags
            delete dataToSave.status;
            delete dataToSave.timestamp;

            // Add userId for validation
            dataToSave.userId = userId;

            const savedProjectId = await GenerationService.saveProjectMetadata(dataToSave);
            currentProjectIdRef.current = savedProjectId;

            if (outputObjectForSave.promptId) {
                try {
                    localStorage.removeItem(`output-${outputObjectForSave.promptId}`);
                } catch (e) { console.error("Error removing from localStorage:", e); }
            }

            showToast('Project saved successfully!', 'success');
            setOutputObjectForSave(null); // Disable button

            // Credits already deducted by stopwatch integration in unified system
            console.log('[ReImagine] 💳 Credits already deducted during generation via stopwatch integration');
            }

        } catch (err) {
             console.error('[ReImagine] Save project failed:', err);
             // err.message should be user-friendly from GenerationServiceError
             showToast(err.message || 'Failed to save project. Please try again.', 'error');
        }
        finally { setSaveInProgress(false); }
    };

    if (isLoadingAuth) {
        return <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}><CircularProgress /></Box>;
    }

    return (
        <Container maxWidth="1280px" sx={{ py: { xs: 3, sm: 4 }, bgcolor: 'background.default', minHeight: 'calc(100vh - 64px)' }}>
             <Stack spacing={4}>
                <Stack spacing={1}>
                    <Heading level="h1" size="4xl">ReImagine Tool</Heading>
                    <Text size="lg" variant="muted">Upload an image, describe the transformation, and let AI create a new version.</Text>
                </Stack>
             <Snackbar open={toast.open} autoHideDuration={4000} onClose={handleCloseToast} anchorOrigin={{ vertical: 'top', horizontal: 'right' }} sx={{ mt: { xs: '64px', sm: '72px' } }} >
                <Alert onClose={handleCloseToast} severity={toast.severity} sx={{ width: '100%' }}> {toast.message} </Alert>
             </Snackbar>

            {/* Main layout structure similar to Img2Video.jsx */}
            <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>

                 {/* Column 1: Input Form */}
                 <Stack sx={{ flex: { md: 1 } }} spacing={2}>
                     <Heading level="h2" size="xl">Input Settings</Heading>
                     <Paper
                         component="form" onSubmit={handleSubmit} elevation={0}
                         sx={{
                             p: 3,
                             borderRadius: 2,
                             border: `1px solid ${theme.palette.divider}`,
                             bgcolor: 'background.paper',
                             display: 'flex', flexDirection: 'column', gap: 2.5,
                             flexGrow: 1,
                             width: '100%'
                         }}
                     >
                         <Text size="base" weight={500} as="label" sx={{ display: 'block', mb: 1 }}>Upload Image</Text>
                         <Stack direction="row" spacing={2} alignItems="center" sx={{ flexWrap: 'wrap' }}>
                             <Button
                                 variant="outlined"
                                 component="label"
                                 startIcon={<UploadFileIcon />}
                                 disabled={!isAuthenticated || loading}
                                 fullWidth={!previewUrl}
                                 sx={{ flexShrink: 0, height: '56px' }}
                             >
                                 Choose Image
                                 <input type="file" onChange={handleFileChange} hidden accept="image/*" required />
                             </Button>
                             {previewUrl && (
                                 <Avatar
                                     variant="rounded"
                                     src={previewUrl}
                                     alt="Input preview"
                                     sx={{
                                         width: 70,
                                         height: 70,
                                         border: `1px solid ${theme.palette.divider}`,
                                         borderRadius: 1.5
                                     }}
                                 />
                             )}
                         </Stack>
                         {file && <Text size="xs" variant="muted" sx={{ wordBreak: 'break-all', mt: 0.5 }}>{file.name}</Text>}

                         <Text size="base" weight={500} as="label" htmlFor="prompt-input" sx={{ display: 'block', mb: 1 }}>Describe Changes (Optional)</Text>
                         <AnimatedTextField
                             id="prompt-input"
                             label=""
                             multiline
                             rows={4}
                             fullWidth
                             value={prompt}
                             onChange={(e) => setPrompt(e.target.value)}
                             disabled={!isAuthenticated || loading}
                             placeholder='e.g., "make it cyberpunk style", "change background to a beach"'
                             InputLabelProps={{ shrink: false }}
                         />

                         <Stack direction="row" spacing={2} alignItems="center" sx={{ flexWrap: 'wrap', mt: 'auto' }}>
                             <Button
                                 type="submit"
                                 variant="contained"
                                 color="primary"
                                 disabled={!isAuthenticated || !file || loading}
                                 sx={{ height: '40px', px: 3, minWidth: '120px' }}
                             >
                                 {loading ? <CircularProgress size={24} color="inherit"/> : 'Generate'}
                             </Button>
                             {/* Stopwatch Display */}
                             <Fade in={loading || (finalTime !== null && (outputUrl || error))} timeout={300}>
                                 <Paper
                                     variant="outlined"
                                     sx={{
                                         display: 'flex',
                                         alignItems: 'center',
                                         gap: 1,
                                         px: 1.5,
                                         py: 0.5,
                                         borderRadius: 1
                                     }}
                                 >
                                     <AccessTimeIcon fontSize="small" sx={{ color: 'text.secondary' }}/>
                                     <Text
                                         size="sm"
                                         weight={500}
                                         as="span"
                                         sx={{
                                             fontFamily: 'monospace',
                                             minWidth: '100px',
                                             color: isStopwatchRunning ? theme.palette.info.main : theme.palette.text.secondary
                                         }}
                                     >
                                         {formattedTime}
                                     </Text>

                                 </Paper>
                             </Fade>
                         </Stack>
                     </Paper>
                 </Stack>

                 {/* Column 2: Output Area */}
                 <Stack sx={{ flex: { md: 1.5 } }} spacing={2}>
                      <Heading level="h2" size="xl">
                          {loading ? 'Generating...' : outputUrl ? 'Output' : 'Output Area'}
                      </Heading>
                      <Paper
                          elevation={0}
                          sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'center',
                              alignItems: 'center',
                              flexGrow: 1,
                              minHeight: { xs: 250, sm: 300, md: 'auto' },
                              borderRadius: 2,
                              border: `1px solid ${theme.palette.divider}`,
                              bgcolor: 'background.paper',
                              p: loading ? 0 : 2,
                              overflow: 'hidden',
                              position: 'relative',
                              width: '100%'
                         }}
                      >
                          {loading && !error && ( <Box sx={{width: '100%', height: '100%', display:'flex', alignItems:'center', justifyContent:'center'}}> <AIGenerationLoader /> </Box> )}
                          {!loading && outputUrl && !error && (
                             <Card elevation={0} sx={{width: '100%', height:'100%', boxShadow:'none', border:'none', background:'transparent', display:'flex', flexDirection:'column'}}>
                                 <Box sx={{ flexGrow: 1, display: 'flex', alignItems:'center', justifyContent:'center', width: '100%', overflow: 'hidden', p: 1 }}>
                                     <CardMedia component="img" src={outputUrl} alt="Generated output" sx={{ display:'block', width: 'auto', height: 'auto', maxHeight: '100%', maxWidth: '100%', objectFit: 'contain', borderRadius: theme.shape.borderRadius / 1.5 }} />
                                 </Box>
                                 <CardContent sx={{ textAlign: 'center', pt: 1, pb: 1 }}>
                                     <Button variant="contained" color="secondary" onClick={handleSaveProject} disabled={!outputObjectForSave || saveInProgress} startIcon={saveInProgress ? <CircularProgress size={20} color="inherit"/> : null} >
                                         {saveInProgress? 'Saving...' : 'Save to My Projects'}
                                     </Button>
                                 </CardContent>
                             </Card>
                          )}
                          {!loading && error && (
                              <Stack spacing={2} alignItems="center" sx={{ textAlign: 'center', p: 3 }}>
                                  <ErrorOutlineIcon sx={{ fontSize: 48, color: 'error.main' }} />
                                  <Heading level="h3" size="lg" color="error.main">Generation Failed</Heading>
                                  <Text variant="muted">{error}</Text>
                                  <Button variant="outlined" onClick={() => setError(null)}>Try Again</Button>
                              </Stack>
                          )}
                          {!loading && !outputUrl && !error && (
                              <Stack spacing={2} alignItems="center" sx={{ textAlign: 'center', p: 3 }}>
                                  <ImageSearchIcon sx={{ fontSize: 48, color: 'text.disabled' }} />
                                  <Text variant="muted">Generated image will appear here</Text>
                              </Stack>
                          )}
                      </Paper>
                 </Stack>
            </Stack>
            </Stack>
        </Container>
    );
}
export default ReImagine;
