import { Request, Response } from 'express';
import { gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import {
  CreditTransactionRequest,
  CreditTransactionResponse,
  TransactionType,
  DeductionSource,
  UserWallet,
  UserPass,
  PassType,
  CreditError,
  CreditErrorType
} from './types';
import { nhost } from '../_utils/nhost';

/**
 * GraphQL query to get user wallet details (LEGACY - Updated for unified credit system)
 */
const GET_USER_WALLET = gql`
  query GetUserWallet($userId: uuid!) {
    user_wallet(where: { user_id: { _eq: $userId } }) {
      id
      user_id
      credits_balance
      last_free_credit_claim
      daily_free_credits_granted
      total_credits_purchased
      active_pass_id
      created_at
      updated_at
    }
  }
`;

/**
 * GraphQL query to get user active pass details
 */
const GET_USER_ACTIVE_PASS = gql`
  query GetUserActivePass($passId: uuid!) {
    user_passes_by_pk(id: $passId) {
      id
      user_id
      pass_type_id
      start_date
      end_date
      status
      created_at
      updated_at
      pass_type {
        id
        name
        type
        credits_per_day
        max_hours
        is_unlimited
        price
        features
        created_at
        updated_at
      }
    }
  }
`;

/**
 * GraphQL mutation to update user wallet (LEGACY - Updated for unified credit system)
 */
const UPDATE_USER_WALLET = gql`
  mutation UpdateUserWallet(
    $walletId: uuid!,
    $creditsBalance: Int!,
    $lastFreeCreditClaim: timestamptz
  ) {
    update_user_wallet_by_pk(
      pk_columns: { id: $walletId },
      _set: {
        credits_balance: $creditsBalance,
        last_free_credit_claim: $lastFreeCreditClaim,
        updated_at: "now()"
      }
    ) {
      id
      credits_balance
      last_free_credit_claim
    }
  }
`;

/**
 * GraphQL mutation to insert credit transaction
 */
const INSERT_CREDIT_TRANSACTION = gql`
  mutation InsertCreditTransaction(
    $id: uuid!,
    $userId: uuid!,
    $amount: Int!,
    $transactionType: String!,
    $balanceAfter: Int!,
    $referenceId: uuid,
    $bundleId: uuid
  ) {
    insert_credit_transactions_one(
      object: {
        id: $id,
        user_id: $userId,
        amount: $amount,
        transaction_type: $transactionType,
        balance_after: $balanceAfter,
        reference_id: $referenceId,
        bundle_id: $bundleId
      }
    ) {
      id
    }
  }
`;

/**
 * GraphQL query to get credit bundle details
 */
const GET_CREDIT_BUNDLE = gql`
  query GetCreditBundle($bundleId: uuid!) {
    credit_bundles_by_pk(id: $bundleId) {
      id
      name
      credits
      price
      duration_days
      is_active
      created_at
      updated_at
    }
  }
`;

/**
 * Handle credit transaction
 * @param req - Express request with transaction details
 * @param res - Express response
 */
export const handleCreditTransaction = async (
  req: Request & { body: CreditTransactionRequest },
  res: Response
): Promise<Response | void> => {
  try {
    const { userId, amount, transactionType, referenceId, bundleId, deductionSource } = req.body;

    // Validate amount
    if (amount <= 0 && transactionType !== TransactionType.CREDIT_USAGE) {
      throw new CreditError(
        CreditErrorType.INVALID_AMOUNT,
        'Transaction amount must be positive',
        400
      );
    }

    if (amount >= 0 && transactionType === TransactionType.CREDIT_USAGE) {
      throw new CreditError(
        CreditErrorType.INVALID_AMOUNT,
        'Credit usage amount must be negative',
        400
      );
    }

    // Get user wallet
    const { data: walletData } = await nhost.graphql.request(GET_USER_WALLET, {
      userId
    });

    if (!walletData || !walletData.user_wallet || walletData.user_wallet.length === 0) {
      throw new CreditError(
        CreditErrorType.WALLET_NOT_FOUND,
        'User wallet not found',
        404
      );
    }

    const userWallet: UserWallet = walletData.user_wallet[0];
    let hasUnlimitedUsage = false;
    let balanceAfter = userWallet.credits_balance;
    let fastBalanceAfter = userWallet.fast_credits_balance;
    let lastFreeCreditDate = userWallet.last_free_credit_date;

    // Check if user has an active pass with unlimited usage
    if (userWallet.active_pass_id) {
      const { data: passData } = await nhost.graphql.request(GET_USER_ACTIVE_PASS, {
        passId: userWallet.active_pass_id
      });

      if (passData && passData.user_passes_by_pk) {
        const userPass: UserPass = passData.user_passes_by_pk;
        
        // Check if pass is still valid
        const now = new Date();
        const endDate = new Date(userPass.end_date);
        
        if (userPass.status === 'active' && endDate > now && userPass.pass_type) {
          const passType: PassType = userPass.pass_type;

          // Check if pass has unlimited usage
          if (passType.is_unlimited && transactionType === TransactionType.CREDIT_USAGE) {
            hasUnlimitedUsage = true;
          }
        }
      }
    }

    // Process transaction based on type
    switch (transactionType) {
      case TransactionType.CREDIT_PURCHASE:
        if (!bundleId) {
          throw new CreditError(
            CreditErrorType.BUNDLE_NOT_FOUND,
            'Bundle ID is required for credit purchases',
            400
          );
        }

        // Get bundle details
        const { data: bundleData } = await nhost.graphql.request(GET_CREDIT_BUNDLE, {
          bundleId
        });

        if (!bundleData || !bundleData.credit_bundles_by_pk) {
          throw new CreditError(
            CreditErrorType.BUNDLE_NOT_FOUND,
            'Credit bundle not found',
            404
          );
        }

        // Add credits to user balance
        balanceAfter = userWallet.credits_balance + amount;
        break;

      case TransactionType.CREDIT_USAGE:
        // Skip balance check if user has unlimited usage
        if (!hasUnlimitedUsage) {
          // Determine which balance to deduct from
          if (deductionSource === DeductionSource.FAST) {
            if (userWallet.fast_credits_balance + amount < 0) {
              throw new CreditError(
                CreditErrorType.INSUFFICIENT_BALANCE,
                'Insufficient fast credits balance',
                400
              );
            }
            fastBalanceAfter = userWallet.fast_credits_balance + amount;
          } else {
            if (userWallet.credits_balance + amount < 0) {
              throw new CreditError(
                CreditErrorType.INSUFFICIENT_BALANCE,
                'Insufficient credits balance',
                400
              );
            }
            balanceAfter = userWallet.credits_balance + amount;
          }
        }
        break;

      case TransactionType.DAILY_REWARD:
        // Update last free credit date
        lastFreeCreditDate = new Date().toISOString();
        balanceAfter = userWallet.credits_balance + amount;
        break;

      case TransactionType.SUBSCRIPTION_CREDIT:
      case TransactionType.PROMO_CREDIT:
        balanceAfter = userWallet.credits_balance + amount;
        break;

      case TransactionType.REFUND:
        if (deductionSource === DeductionSource.FAST) {
          fastBalanceAfter = userWallet.fast_credits_balance + amount;
        } else {
          balanceAfter = userWallet.credits_balance + amount;
        }
        break;

      default:
        throw new CreditError(
          CreditErrorType.INVALID_TRANSACTION_TYPE,
          'Invalid transaction type',
          400
        );
    }

    // If we're not using unlimited credits, update the wallet
    if (!hasUnlimitedUsage || transactionType !== TransactionType.CREDIT_USAGE) {
      await nhost.graphql.request(UPDATE_USER_WALLET, {
        walletId: userWallet.id,
        creditsBalance: balanceAfter,
        fastCreditsBalance: fastBalanceAfter,
        lastFreeCreditDate
      });
    }

    // Record the transaction
    const transactionId = uuidv4();
    await nhost.graphql.request(INSERT_CREDIT_TRANSACTION, {
      id: transactionId,
      userId,
      amount,
      transactionType,
      balanceAfter: deductionSource === DeductionSource.FAST ? fastBalanceAfter : balanceAfter,
      referenceId,
      bundleId
    });

    // Return success response
    const response: CreditTransactionResponse = {
      success: true,
      message: 'Transaction completed successfully',
      transactionId,
      balanceAfter,
      fastBalanceAfter,
      hasUnlimitedUsage
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Credit transaction error:', error);

    if (error instanceof CreditError) {
      res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'An unexpected error occurred',
        type: CreditErrorType.DATABASE_ERROR
      });
    }
  }
};

/**
 * Express handler for credit transactions
 */
export default async (req: Request, res: Response): Promise<Response | void> => {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  await handleCreditTransaction(req, res);
};

