// src/pages/Login.jsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  Container, Typo<PERSON>, TextField, Button, Box, Alert, Link as MuiLink,
  CircularProgress, InputAdornment, IconButton, Checkbox, FormControlLabel,
  Divider
} from '@mui/material';
import { useSignInEmailPassword } from '@nhost/react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useSnackbar } from 'notistack'; // Import useSnackbar
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import {
  validateEmail,
  validateLoginForm,
  debounce
} from '../utils/validation';

function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState(''); // Keep for form-level errors
  const [formErrors, setFormErrors] = useState({ email: '', password: '' });
  const [formTouched, setFormTouched] = useState({ email: false, password: false });
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar(); // Use snackbar for feedback

  // Use Nhost login hook
  const {
    signInEmailPassword,
    isLoading,
    isSuccess,
    error: nhostError,
    needsEmailVerification
  } = useSignInEmailPassword();

  // Load remembered email if available
  useEffect(() => {
    const savedEmail = localStorage.getItem('rememberedEmail');
    if (savedEmail) {
      setEmail(savedEmail);
      setRememberMe(true);
    }
  }, []);

  // Handle login success and verification needs
  useEffect(() => {
    if (isSuccess) {
      console.log('Login successful, checking user verification status');

      // Save email if remember me is checked
      if (rememberMe) {
        localStorage.setItem('rememberedEmail', email);
      } else {
        localStorage.removeItem('rememberedEmail');
      }

      // Check if user needs email verification by examining the user data
      // We'll let the ProtectedRoute handle the verification redirect
      enqueueSnackbar('✅ Welcome back! Login successful.', {
        variant: 'success',
        autoHideDuration: 3000,
        anchorOrigin: { vertical: 'top', horizontal: 'center' }
      });

      // Store email for potential verification page
      localStorage.setItem('pendingVerificationEmail', email.trim());
      sessionStorage.setItem('pendingVerificationEmail', email.trim());

      // Navigate to dashboard - ProtectedRoute will handle verification redirect if needed
      navigate('/dashboard', { replace: true });
    }
  }, [isSuccess, email, rememberMe, navigate, enqueueSnackbar]);

  // Handle login errors
  useEffect(() => {
    if (nhostError) {
      console.error("Nhost Login failed:", nhostError);

      // Map Nhost errors to user-friendly messages and handle specific cases
      let errorMessage = 'Login failed. Please try again.';
      let fieldErrors = {};

      if (nhostError.message?.includes('invalid-email-password') ||
          nhostError.message?.includes('invalid credentials')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
        fieldErrors.password = 'Invalid email or password';

        // Show specific toast for invalid credentials
        enqueueSnackbar('❌ Invalid email or password. Please check your credentials and try again.', {
          variant: 'error',
          autoHideDuration: 8000,
          anchorOrigin: { vertical: 'top', horizontal: 'center' }
        });
      } else if (nhostError.message?.includes('unverified') ||
                 nhostError.message?.includes('verification') ||
                 nhostError.message?.includes('email-not-verified')) {
        errorMessage = 'Please verify your email address before logging in.';

        // Store email for verification page
        localStorage.setItem('pendingVerificationEmail', email.trim());
        sessionStorage.setItem('pendingVerificationEmail', email.trim());

        // Show specific toast for verification issues
        enqueueSnackbar('📧 Please verify your email address before logging in.', {
          variant: 'warning',
          autoHideDuration: 8000,
          anchorOrigin: { vertical: 'top', horizontal: 'center' }
        });

        // Redirect to verification page after a short delay
        setTimeout(() => {
          navigate('/verify-email', { replace: true });
        }, 2000);
      } else if (nhostError.message?.includes('network') ||
                 nhostError.message?.includes('connection')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';

        // Show specific toast for network issues
        enqueueSnackbar('🌐 Network error. Please check your internet connection and try again.', {
          variant: 'error',
          autoHideDuration: 8000,
          anchorOrigin: { vertical: 'top', horizontal: 'center' }
        });
      } else if (nhostError.message?.includes('rate limit') ||
                 nhostError.message?.includes('too many')) {
        errorMessage = 'Too many login attempts. Please wait a moment before trying again.';

        // Show specific toast for rate limiting
        enqueueSnackbar('⏰ Too many login attempts. Please wait a moment before trying again.', {
          variant: 'warning',
          autoHideDuration: 10000,
          anchorOrigin: { vertical: 'top', horizontal: 'center' }
        });
      } else {
        // Generic error toast
        enqueueSnackbar(`❌ ${nhostError.message || errorMessage}`, {
          variant: 'error',
          autoHideDuration: 6000,
          anchorOrigin: { vertical: 'top', horizontal: 'center' }
        });
      }

      // Update form errors
      setFormErrors(prev => ({ ...prev, ...fieldErrors }));
      setError(errorMessage);
    }
  }, [nhostError, enqueueSnackbar]);

  // Debounced validation function
  const debouncedValidation = useCallback(
    debounce((name, value) => {
      let errorMessage = '';

      if (name === 'email') {
        const emailValidation = validateEmail(value);
        errorMessage = emailValidation.isValid ? '' : emailValidation.message;
      } else if (name === 'password') {
        errorMessage = !value ? 'Password is required' : '';
      }

      setFormErrors(prev => ({ ...prev, [name]: errorMessage }));
    }, 300),
    []
  );

  // Handle field change with validation
  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === 'email') {
      setEmail(value);
    } else if (name === 'password') {
      setPassword(value);
    }

    // Mark field as touched
    setFormTouched(prev => ({ ...prev, [name]: true }));

    // Debounced validation for better UX
    if (formTouched[name]) {
      debouncedValidation(name, value);
    }
  };

  // Handle field blur for validation
  const handleBlur = (e) => {
    const { name, value } = e.target;
    setFormTouched(prev => ({ ...prev, [name]: true }));

    // Immediate validation on blur
    let errorMessage = '';
    if (name === 'email') {
      const emailValidation = validateEmail(value);
      errorMessage = emailValidation.isValid ? '' : emailValidation.message;
    } else if (name === 'password') {
      errorMessage = !value ? 'Password is required' : '';
    }

    setFormErrors(prev => ({ ...prev, [name]: errorMessage }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate all fields using the validation utility
    const formValidation = validateLoginForm({ email, password });

    setFormErrors(formValidation.errors);
    setFormTouched({ email: true, password: true });

    // If there are validation errors, don't proceed
    if (!formValidation.isValid) {
      setError('Please correct the errors before logging in.');
      return;
    }

    setError(''); // Clear previous form errors

    // Use Nhost hook to sign in user
    await signInEmailPassword(email.trim(), password);
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Container component="main" maxWidth="xs" sx={{ mt: { xs: 4, sm: 8 }, mb: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2, border: theme => `1px solid ${theme.palette.divider}`, boxShadow: 1 }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Typography component="h1" variant="h4" align="center" gutterBottom sx={{ mb: 3 }}>
          Login
        </Typography>

        {/* Form-level error alert */}
        {error && (
          <Alert
            severity="error"
            sx={{ width: '100%', mb: 2 }}
            onClose={() => setError('')}
            icon={<ErrorOutlineIcon />}
          >
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit} noValidate sx={{ width: '100%' }}>
          {/* Email Field */}
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email Address"
            name="email"
            type="email"
            autoComplete="email"
            autoFocus
            value={email}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={isLoading}
            error={formTouched.email && Boolean(formErrors.email)}
            helperText={formTouched.email && formErrors.email}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <EmailIcon color="action" />
                </InputAdornment>
              ),
            }}
            aria-label="Email address"
          />

          {/* Password Field */}
          <TextField
            margin="normal"
            required
            fullWidth
            name="password"
            label="Password"
            type={showPassword ? 'text' : 'password'}
            id="password"
            autoComplete="current-password"
            value={password}
            onChange={handleChange}
            onBlur={handleBlur}
            disabled={isLoading}
            error={formTouched.password && Boolean(formErrors.password)}
            helperText={formTouched.password && formErrors.password}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LockIcon color="action" />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={togglePasswordVisibility}
                    edge="end"
                    size="small"
                  >
                    {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            aria-label="Password"
          />

          {/* Remember Me Checkbox */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 1 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  color="primary"
                  size="small"
                />
              }
              label={<Typography variant="body2">Remember me</Typography>}
            />
            <MuiLink component={RouterLink} to="/forgot-password" variant="body2">
              Forgot password?
            </MuiLink>
          </Box>

          {/* Login Button */}
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2, py: 1.5 }}
            disabled={isLoading}
            aria-label="Login button"
          >
            {isLoading ? <CircularProgress size={24} color="inherit" /> : 'Login'}
          </Button>

          <Divider sx={{ my: 2 }}>
            <Typography variant="body2" color="text.secondary">OR</Typography>
          </Divider>

          {/* Register Link */}
          <Typography variant="body2" align="center">
            Don't have an account?{' '}
            <MuiLink component={RouterLink} to="/register" variant="body2">
              Register here
            </MuiLink>
          </Typography>
        </Box>
      </Box>
    </Container>
  );
}

export default Login;
