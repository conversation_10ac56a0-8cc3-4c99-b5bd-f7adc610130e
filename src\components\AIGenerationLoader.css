/* src/components/AIGenerationLoader.css */

.aigen-loader-pov {
  width: 100%;
  height: 100%; /* Fill the parent Paper/Box */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: transparent !important; /* Explicitly transparent */
}

.aigen-loader-tray {
  width: 90%; /* Allow tray to be slightly smaller than POV */
  max-width: 350px; /* Adjust max width */
  transform-style: preserve-3d;
  /* Height is dynamic */
}

.aigen-loader-die {
  width: 100%;
  height: 40px; /* Further reduce height */
  padding-bottom: 5px; /* Adjust spacing */
  perspective: 700px;
  margin: 0 auto;
  transform-style: preserve-3d;
  opacity: 0; /* GSAP fade-in */
  position: relative;
}

.aigen-loader-cube {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

.aigen-loader-face {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center; /* Vertical center */
  justify-content: center; /* Horizontal center */
  font-family: "Montserrat", sans-serif;
  font-weight: 900;
  text-transform: uppercase;
  /* Color is controlled by GSAP HSL */
  /* background-color: transparent; */ /* Remove background */
  backface-visibility: hidden;
  border: none; /* Remove border */
  border-radius: 3px;
  user-select: none;
  font-size: 14px; /* Small font */
  padding: 0 4px;
  box-sizing: border-box;
  text-align: center;
  line-height: 1; /* Tight line height */
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: inherit; /* Inherit color from parent if needed, but GSAP overrides */
}