# Testing Guide: Responsive Navbar & Dashboard Changes

## Quick Testing Checklist

### 1. Desktop Navbar Positioning (>768px)
- [ ] **Fixed Sidebar**: Sidebar stays in place when scrolling page content
- [ ] **No AppBar**: Hamburger menu should not be visible
- [ ] **Content Spacing**: Main content has proper left margin (250px)
- [ ] **Full Height**: Sidebar extends full viewport height
- [ ] **Scrollable Content**: Sidebar content scrolls if needed

### 2. Mobile Navigation (≤768px)
- [ ] **AppBar Visible**: Top bar with hamburger menu appears
- [ ] **Overlay Drawer**: Clicking hamburger opens overlay drawer
- [ ] **Close Behavior**: Drawer closes when clicking outside or on nav items
- [ ] **Content Margin**: Main content has top margin for AppBar (64px)
- [ ] **No Sidebar**: Permanent sidebar should be hidden

### 3. Responsive Transitions
- [ ] **Smooth Switching**: Resize browser window across 768px breakpoint
- [ ] **State Management**: Mobile drawer closes when switching to desktop
- [ ] **Layout Adjustment**: Content margins adjust smoothly
- [ ] **No Glitches**: No visual artifacts during transitions

### 4. Dashboard Wallet Section
- [ ] **Side-by-Side Layout**: Wallet Balance (left) + Usage Statistics (right) on desktop
- [ ] **Stacked Mobile**: Components stack vertically on mobile
- [ ] **Credit Display**: Regular and Fast credits show correctly
- [ ] **Daily Credits**: Claim button works (if available)
- [ ] **Statistics**: Usage stats display with proper icons and values

### 5. Project Carousels
- [ ] **Horizontal Scrolling**: Projects display in horizontal carousel
- [ ] **Navigation Arrows**: Left/right arrows appear on desktop (when multiple items)
- [ ] **Touch Scrolling**: Swipe works on mobile devices
- [ ] **Consistent Sizing**: All project cards have uniform width (280-300px)
- [ ] **Empty States**: Proper message when no projects exist

### 6. Active Pass Section
- [ ] **Conditional Display**: Only shows when user has active pass
- [ ] **Full Width**: Takes full width below wallet/usage sections
- [ ] **Pass Details**: Shows pass type, expiration, hours used
- [ ] **Progress Bar**: Hours used progress displays correctly

## Detailed Testing Scenarios

### Scenario 1: Desktop Experience (1920x1080)
1. Open application in desktop browser
2. Navigate to dashboard
3. Verify fixed sidebar on left side
4. Scroll page content - sidebar should remain fixed
5. Check wallet section has side-by-side layout
6. Test project carousel navigation arrows
7. Resize window to test responsive behavior

### Scenario 2: Mobile Experience (375x667)
1. Open application in mobile browser or dev tools mobile view
2. Verify AppBar with hamburger menu
3. Tap hamburger to open drawer
4. Test navigation items
5. Verify drawer closes when tapping outside
6. Check wallet components stack vertically
7. Test project carousel touch scrolling

### Scenario 3: Tablet Experience (768x1024)
1. Test at exactly 768px width (breakpoint)
2. Verify behavior switches correctly
3. Test both portrait and landscape orientations
4. Check layout adapts appropriately

### Scenario 4: Window Resizing
1. Start with desktop view (>768px)
2. Slowly resize window to mobile (<768px)
3. Verify smooth transitions
4. Check mobile drawer closes automatically
5. Resize back to desktop
6. Verify layout returns to desktop mode

## Browser Testing Matrix

### Desktop Browsers
- [ ] **Chrome 90+**: Full functionality
- [ ] **Firefox 88+**: CSS Grid and Flexbox support
- [ ] **Safari 14+**: Webkit-specific features
- [ ] **Edge 90+**: Chromium-based compatibility

### Mobile Browsers
- [ ] **Chrome Mobile**: Touch interactions
- [ ] **Safari iOS**: iOS-specific behaviors
- [ ] **Firefox Mobile**: Alternative rendering engine
- [ ] **Samsung Internet**: Android default browser

## Performance Testing

### Metrics to Monitor
- [ ] **First Contentful Paint**: < 2 seconds
- [ ] **Largest Contentful Paint**: < 3 seconds
- [ ] **Cumulative Layout Shift**: < 0.1
- [ ] **First Input Delay**: < 100ms

### Performance Scenarios
1. **Slow Network**: Test with throttled connection
2. **Large Datasets**: Test with many projects in carousel
3. **Rapid Resizing**: Test window resize performance
4. **Memory Usage**: Monitor for memory leaks during navigation

## Accessibility Testing

### Screen Reader Testing
- [ ] **Navigation Structure**: Proper heading hierarchy
- [ ] **ARIA Labels**: Meaningful labels for interactive elements
- [ ] **Focus Management**: Logical tab order
- [ ] **Announcements**: Status changes announced properly

### Keyboard Navigation
- [ ] **Tab Order**: Logical focus flow
- [ ] **Enter/Space**: Activates buttons and links
- [ ] **Escape**: Closes modal dialogs and drawers
- [ ] **Arrow Keys**: Navigate carousel items

### Visual Accessibility
- [ ] **Color Contrast**: WCAG AA compliance
- [ ] **Focus Indicators**: Visible focus outlines
- [ ] **Text Scaling**: Readable at 200% zoom
- [ ] **Reduced Motion**: Respects user preferences

## Error Scenarios

### Network Errors
- [ ] **API Failures**: Graceful error handling
- [ ] **Timeout Errors**: Proper retry mechanisms
- [ ] **Offline Mode**: Appropriate offline messaging

### Data Errors
- [ ] **Empty States**: Proper empty state messages
- [ ] **Invalid Data**: Handles malformed responses
- [ ] **Missing Images**: Fallback images display

### User Errors
- [ ] **Invalid Actions**: Prevents invalid operations
- [ ] **Form Validation**: Clear error messages
- [ ] **Permission Errors**: Appropriate access denied messages

## Regression Testing

### Previous Functionality
- [ ] **Authentication**: Login/logout still works
- [ ] **Project Management**: Save/delete projects functional
- [ ] **Credit System**: Credit deduction works correctly
- [ ] **File Uploads**: Image/video uploads functional
- [ ] **Generation Tools**: All tools still accessible

### Integration Points
- [ ] **Nhost Integration**: Authentication and database queries
- [ ] **Storage System**: File upload and retrieval
- [ ] **GraphQL**: All queries and mutations work
- [ ] **Credit Service**: Wallet operations functional

## Bug Reporting Template

When reporting issues, include:

```
**Environment:**
- Browser: [Chrome 90, Firefox 88, etc.]
- Device: [Desktop, Mobile, Tablet]
- Screen Size: [1920x1080, 375x667, etc.]
- OS: [Windows 10, macOS, iOS, Android]

**Steps to Reproduce:**
1. [First step]
2. [Second step]
3. [Third step]

**Expected Behavior:**
[What should happen]

**Actual Behavior:**
[What actually happens]

**Screenshots/Video:**
[Attach visual evidence]

**Console Errors:**
[Any JavaScript errors in console]

**Additional Context:**
[Any other relevant information]
```

## Success Criteria

### Must Have ✅
- [x] Fixed desktop sidebar positioning
- [x] Mobile overlay drawer functionality
- [x] Responsive transitions at 768px breakpoint
- [x] Side-by-side wallet/usage layout on desktop
- [x] Carousel layout for project sections
- [x] Consistent project card sizing
- [x] Smooth animations and transitions

### Should Have ✅
- [x] Touch scrolling for mobile carousels
- [x] Navigation arrows for desktop carousels
- [x] Loading states and error handling
- [x] Accessibility compliance
- [x] Performance optimization
- [x] Cross-browser compatibility

### Nice to Have 🔄
- [ ] Gesture support for carousel navigation
- [ ] Keyboard shortcuts for navigation
- [ ] Advanced animation preferences
- [ ] Progressive enhancement features

## Final Verification

Before marking complete, ensure:
1. All manual tests pass
2. No console errors in browser
3. Responsive behavior works correctly
4. Performance meets targets
5. Accessibility standards met
6. Cross-browser compatibility verified
