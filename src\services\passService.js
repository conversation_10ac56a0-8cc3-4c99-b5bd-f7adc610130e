// src/services/passService.js
import { nhost } from './nhost';
import { ensureValidSession } from './auth';
import { GraphQLServiceError, PassServiceError } from './errors';

// GraphQL queries
const GET_USER_ACTIVE_PASS = `
  query GetUserActivePass($userId: uuid!) {
    user_wallet(where: { user_id: { _eq: $userId } }) {
      user_id
      active_pass_id
    }
    user_passes(
      where: {
        user_id: { _eq: $userId },
        status: { _eq: "active" },
        _or: [
          { end_date: { _is_null: true } },
          { end_date: { _gte: "now()" } }
        ]
      }
    ) {
      id
      user_id
      pass_type_id
      start_date
      end_date
      status
      hours_used
      created_at
      updated_at
      pass_type {
        id
        name
        tier
        category
        monthly_price
        hours_included
        is_unlimited
        slow_generation_limit
        fast_generation_limit
        features
        created_at
        updated_at
      }
    }
  }
`;

const GET_AVAILABLE_PASSES = `
  query GetAvailablePasses {
    pass_types(where: { is_active: { _eq: true } }, order_by: { sort_order: asc }) {
      id
      name
      tier
      category
      monthly_price
      hours_included
      is_unlimited
      slow_generation_limit
      fast_generation_limit
      provider_price_id
      features
      is_active
      sort_order
      created_at
      updated_at
    }
  }
`;

/**
 * Get user's current active pass
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - User's active pass details
 */
async function getUserActivePass(userId) {
  try {
    if (!userId) {
      throw new PassServiceError('User ID is required');
    }

    // Ensure we have a valid session before making the request
    const sessionResult = await ensureValidSession();
    if (!sessionResult.success) {
      throw new GraphQLServiceError('Authentication required', [], sessionResult.error);
    }

    const { data, error } = await nhost.graphql.request(GET_USER_ACTIVE_PASS, { userId });
    
    if (error) {
      throw new GraphQLServiceError('Failed to fetch user pass', error.response?.errors, error);
    }
    
    if (!data?.user_wallet?.[0]) {
      return { hasActivePass: false };
    }

    const wallet = data.user_wallet[0];
    const activePasses = data.user_passes || [];
    const activePass = activePasses.find(pass =>
      pass.status === 'active' &&
      (!pass.end_date || new Date(pass.end_date) > new Date())
    );

    return {
      hasActivePass: !!activePass,
      activePass: activePass || null,
      wallet: wallet
    };
  } catch (error) {
    console.error('[PassService] Error fetching user pass:', error);
    if (error instanceof GraphQLServiceError || error instanceof PassServiceError) {
      throw error;
    }
    throw new PassServiceError(`Failed to get user active pass: ${error.message}`, error);
  }
}

/**
 * Get all available pass types
 * @returns {Promise<Array>} - Available pass types
 */
async function getAvailablePasses() {
  try {
    // Ensure we have a valid session before making the request
    const sessionResult = await ensureValidSession();
    if (!sessionResult.success) {
      throw new GraphQLServiceError('Authentication required', [], sessionResult.error);
    }

    const { data, error } = await nhost.graphql.request(GET_AVAILABLE_PASSES);
    
    if (error) {
      throw new GraphQLServiceError('Failed to fetch available passes', error.response?.errors, error);
    }
    
    return data?.pass_types || [];
  } catch (error) {
    console.error('[PassService] Error fetching available passes:', error);
    if (error instanceof GraphQLServiceError) {
      throw error;
    }
    throw new PassServiceError(`Failed to get available passes: ${error.message}`, error);
  }
}

/**
 * Get allowed server types for user
 * @param {string} userId - User ID
 * @returns {Promise<Array>} Allowed server types
 */
async function getAllowedServerTypes(userId) {
  try {
    const passInfo = await getUserActivePass(userId);

    if (!passInfo.hasActivePass) {
      return ['slow']; // Default to slow only
    }

    const pass = passInfo.activePass;
    const tier = pass.pass_type.tier;
    const category = pass.pass_type.category;

    // Server access rules
    if (category === 'Visitor') {
      if (tier === 'Free') {
        return ['slow'];
      } else if (tier === 'Paid') {
        return ['slow', 'fast'];
      }
    } else if (category === 'Citizen') {
      return ['slow', 'fast']; // All citizen tiers get both
    }

    return ['slow']; // Default fallback
  } catch (error) {
    console.error('[PassService] Error getting allowed server types:', error);
    throw new PassServiceError(`Failed to get allowed server types: ${error.message}`, error);
  }
}

/**
 * Check if user should be auto-downgraded
 * @param {string} userId - User ID
 * @param {number} creditsBalance - Current credit balance
 * @returns {Promise<Object>} Auto-downgrade check result
 */
async function checkAutoDowngrade(userId, creditsBalance = 0) {
  try {
    const passInfo = await getUserActivePass(userId);

    if (!passInfo.hasActivePass) {
      return { shouldDowngrade: false, reason: 'No active pass' };
    }

    const pass = passInfo.activePass;
    const tier = pass.pass_type.tier;
    const category = pass.pass_type.category;

    // Only Visitor Paid tier auto-downgrades
    if (category === 'Visitor' && tier === 'Paid' && creditsBalance <= 0) {
      return {
        shouldDowngrade: true,
        reason: 'Visitor Paid tier with zero credits',
        currentPassId: pass.id,
        targetTier: 'Free'
      };
    }

    return { shouldDowngrade: false };
  } catch (error) {
    console.error('[PassService] Error checking auto-downgrade:', error);
    throw new PassServiceError(`Failed to check auto-downgrade: ${error.message}`, error);
  }
}

/**
 * Auto-downgrade user pass
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Downgrade result
 */
async function autoDowngradePass(userId) {
  try {
    // Call backend function to handle auto-downgrade
    const { data, error } = await nhost.functions.call(
      'pass/auto-downgrade',
      { userId }
    );

    if (error) {
      console.error(`[PassService] ❌ Auto-downgrade error:`, error);
      throw new PassServiceError(`Failed to auto-downgrade pass: ${error.message}`);
    }

    if (!data || !data.success) {
      const errorMessage = data?.message || 'Unknown error during auto-downgrade';
      console.error(`[PassService] ❌ Auto-downgrade failed:`, errorMessage);
      throw new PassServiceError(errorMessage);
    }

    console.log(`[PassService] ✅ Pass auto-downgraded successfully:`, data);
    return data;
  } catch (error) {
    if (error instanceof PassServiceError) {
      throw error;
    }
    console.error(`[PassService] 💥 Unexpected error during auto-downgrade:`, error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    throw new PassServiceError(message, error);
  }
}

/**
 * Upgrade user pass
 * @param {string} userId - User ID
 * @param {string} newPassId - New pass type ID
 * @returns {Promise<Object>} Upgrade result
 */
async function upgradePass(userId, newPassId) {
  try {
    if (!userId || !newPassId) {
      throw new PassServiceError('User ID and new pass ID are required');
    }

    // Call backend function to handle pass upgrade
    const { data, error } = await nhost.functions.call(
      'pass/upgrade',
      { userId, newPassId }
    );

    if (error) {
      console.error(`[PassService] ❌ Pass upgrade error:`, error);
      throw new PassServiceError(`Failed to upgrade pass: ${error.message}`);
    }

    if (!data || !data.success) {
      const errorMessage = data?.message || 'Unknown error during pass upgrade';
      console.error(`[PassService] ❌ Pass upgrade failed:`, errorMessage);
      throw new PassServiceError(errorMessage);
    }

    console.log(`[PassService] ✅ Pass upgraded successfully:`, data);
    return data;
  } catch (error) {
    if (error instanceof PassServiceError) {
      throw error;
    }
    console.error(`[PassService] 💥 Unexpected error during pass upgrade:`, error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    throw new PassServiceError(message, error);
  }
}

/**
 * Check pass expiry
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Expiry check result
 */
async function checkPassExpiry(userId) {
  try {
    const passInfo = await getUserActivePass(userId);

    if (!passInfo.hasActivePass) {
      return { isExpired: false, reason: 'No active pass' };
    }

    const pass = passInfo.activePass;

    if (!pass.end_date) {
      return { isExpired: false, reason: 'Pass has no expiry date' };
    }

    const now = new Date();
    const endDate = new Date(pass.end_date);

    if (endDate <= now) {
      return {
        isExpired: true,
        expiredDate: pass.end_date,
        passId: pass.id
      };
    }

    return {
      isExpired: false,
      expiresAt: pass.end_date,
      daysRemaining: Math.ceil((endDate - now) / (1000 * 60 * 60 * 24))
    };
  } catch (error) {
    console.error('[PassService] Error checking pass expiry:', error);
    throw new PassServiceError(`Failed to check pass expiry: ${error.message}`, error);
  }
}

export const PassService = {
  getUserActivePass,
  getAvailablePasses,
  getAllowedServerTypes,
  checkAutoDowngrade,
  autoDowngradePass,
  upgradePass,
  checkPassExpiry
};
