// src/services/serverAccessService.js
// Server access logic for the unified credit system

import { nhost } from './nhost';
import { ensureValidSession } from './auth';
import { GraphQLServiceError, ServerAccessError } from './errors';
import { canAccessServerType, getAvailableServerTypes, getServerCostInfo } from '../utils/passUtils';

// GraphQL Queries
const GET_AVAILABLE_SERVERS = `
  query GetAvailableServers($toolId: String) {
    comfyui_servers(
      where: {
        is_active: { _eq: true },
        _and: [
          { _or: [
            { tool_id: { _is_null: true } },
            { tool_id: { _eq: $toolId } }
          ]}
        ]
      },
      order_by: { queue_priority: asc }
    ) {
      id
      url
      server_type
      is_active
      tool_id
      queue_priority
      credits_per_second
      max_concurrent_jobs
      current_jobs
      min_pass_tier
      allowed_categories
    }
  }
`;

const GET_SERVER_BY_ID = `
  query GetServerById($serverId: uuid!) {
    comfyui_servers_by_pk(id: $serverId) {
      id
      url
      server_type
      is_active
      tool_id
      queue_priority
      credits_per_second
      max_concurrent_jobs
      current_jobs
      min_pass_tier
      allowed_categories
    }
  }
`;

/**
 * Get available servers for user based on their pass and credits
 * @param {string} userId - User ID
 * @param {string} toolId - Tool ID (optional)
 * @returns {Promise<Array>} Available servers
 */
export async function getAvailableServers(userId, toolId = null) {
  try {
    if (!userId) {
      throw new ServerAccessError('User ID is required');
    }

    // Ensure valid session
    const sessionResult = await ensureValidSession();
    if (!sessionResult.success) {
      throw new GraphQLServiceError('Authentication required', [], sessionResult.error);
    }

    // Get servers from database
    const { data, error } = await nhost.graphql.request(GET_AVAILABLE_SERVERS, { toolId });
    
    if (error) {
      throw new GraphQLServiceError('Failed to fetch servers', error.response?.errors, error);
    }

    const servers = data?.comfyui_servers || [];
    
    // Filter servers based on user's pass and credits
    // Note: This would typically require user's pass info, but for now return all active servers
    // In a real implementation, you'd fetch user's pass and filter accordingly
    
    return servers.map(server => ({
      ...server,
      costInfo: getServerCostInfo(server.server_type),
      isAvailable: server.current_jobs < server.max_concurrent_jobs
    }));

  } catch (error) {
    console.error('[ServerAccessService] Error fetching available servers:', error);
    if (error instanceof GraphQLServiceError || error instanceof ServerAccessError) {
      throw error;
    }
    throw new ServerAccessError(`Failed to get available servers: ${error.message}`);
  }
}

/**
 * Select optimal server for user based on preferences and availability
 * @param {string} userId - User ID
 * @param {string} toolId - Tool ID
 * @param {string} preferredType - Preferred server type ('slow' or 'fast')
 * @param {Object} userPass - User's current pass
 * @param {number} creditsBalance - User's credit balance
 * @returns {Promise<Object>} Selected server or null
 */
export async function selectOptimalServer(userId, toolId, preferredType = 'slow', userPass = null, creditsBalance = 0) {
  try {
    const availableServers = await getAvailableServers(userId, toolId);
    
    if (!availableServers.length) {
      return {
        server: null,
        reason: 'No servers available'
      };
    }

    // If user pass is provided, check access
    if (userPass) {
      const accessCheck = canAccessServerType(
        userPass.tier, 
        userPass.category, 
        preferredType, 
        creditsBalance
      );
      
      if (!accessCheck.allowed) {
        // Try to fallback to slow server if fast was requested
        if (preferredType === 'fast') {
          const slowAccessCheck = canAccessServerType(
            userPass.tier, 
            userPass.category, 
            'slow', 
            creditsBalance
          );
          
          if (slowAccessCheck.allowed) {
            preferredType = 'slow';
          } else {
            return {
              server: null,
              reason: accessCheck.reason,
              suggestedUpgrade: accessCheck.suggestedUpgrade
            };
          }
        } else {
          return {
            server: null,
            reason: accessCheck.reason
          };
        }
      }
    }

    // Filter servers by type and availability
    const suitableServers = availableServers.filter(server => 
      server.server_type === preferredType && 
      server.isAvailable &&
      server.is_active
    );

    if (!suitableServers.length) {
      // Try alternative server type if none available
      const alternativeType = preferredType === 'fast' ? 'slow' : 'fast';
      const alternativeServers = availableServers.filter(server => 
        server.server_type === alternativeType && 
        server.isAvailable &&
        server.is_active
      );

      if (alternativeServers.length && userPass) {
        const altAccessCheck = canAccessServerType(
          userPass.tier, 
          userPass.category, 
          alternativeType, 
          creditsBalance
        );
        
        if (altAccessCheck.allowed) {
          // Select best alternative server
          const selectedServer = alternativeServers.sort((a, b) => a.queue_priority - b.queue_priority)[0];
          return {
            server: selectedServer,
            reason: `${preferredType} servers busy, using ${alternativeType} server`,
            typeChanged: true
          };
        }
      }

      return {
        server: null,
        reason: `No ${preferredType} servers available`
      };
    }

    // Select server with lowest queue priority (highest priority)
    const selectedServer = suitableServers.sort((a, b) => a.queue_priority - b.queue_priority)[0];
    
    return {
      server: selectedServer,
      reason: 'Optimal server selected'
    };

  } catch (error) {
    console.error('[ServerAccessService] Error selecting optimal server:', error);
    throw new ServerAccessError(`Failed to select optimal server: ${error.message}`);
  }
}

/**
 * Validate server access for user
 * @param {string} userId - User ID
 * @param {string} serverId - Server ID
 * @param {Object} userPass - User's current pass
 * @param {number} creditsBalance - User's credit balance
 * @returns {Promise<Object>} Access validation result
 */
export async function validateServerAccess(userId, serverId, userPass = null, creditsBalance = 0) {
  try {
    if (!userId || !serverId) {
      throw new ServerAccessError('User ID and Server ID are required');
    }

    // Get server details
    const { data, error } = await nhost.graphql.request(GET_SERVER_BY_ID, { serverId });
    
    if (error) {
      throw new GraphQLServiceError('Failed to fetch server details', error.response?.errors, error);
    }

    const server = data?.comfyui_servers_by_pk;
    
    if (!server) {
      return {
        allowed: false,
        reason: 'Server not found'
      };
    }

    if (!server.is_active) {
      return {
        allowed: false,
        reason: 'Server is not active'
      };
    }

    if (server.current_jobs >= server.max_concurrent_jobs) {
      return {
        allowed: false,
        reason: 'Server is at capacity'
      };
    }

    // Check user access if pass is provided
    if (userPass) {
      const accessCheck = canAccessServerType(
        userPass.tier, 
        userPass.category, 
        server.server_type, 
        creditsBalance
      );
      
      if (!accessCheck.allowed) {
        return {
          allowed: false,
          reason: accessCheck.reason,
          suggestedUpgrade: accessCheck.suggestedUpgrade
        };
      }
    }

    return {
      allowed: true,
      server,
      costInfo: getServerCostInfo(server.server_type)
    };

  } catch (error) {
    console.error('[ServerAccessService] Error validating server access:', error);
    if (error instanceof GraphQLServiceError || error instanceof ServerAccessError) {
      throw error;
    }
    throw new ServerAccessError(`Failed to validate server access: ${error.message}`);
  }
}

/**
 * Get server cost information
 * @param {string} serverId - Server ID
 * @returns {Promise<Object>} Server cost information
 */
export async function getServerCostInfo(serverId) {
  try {
    const { data, error } = await nhost.graphql.request(GET_SERVER_BY_ID, { serverId });
    
    if (error) {
      throw new GraphQLServiceError('Failed to fetch server details', error.response?.errors, error);
    }

    const server = data?.comfyui_servers_by_pk;
    
    if (!server) {
      throw new ServerAccessError('Server not found');
    }

    return {
      serverId: server.id,
      serverType: server.server_type,
      creditsPerSecond: server.credits_per_second || 1,
      displayName: server.server_type === 'fast' ? 'Fast Server' : 'Slow Server',
      description: server.server_type === 'fast' ? 
        'High-performance processing with faster results' : 
        'Standard processing with reliable results'
    };

  } catch (error) {
    console.error('[ServerAccessService] Error getting server cost info:', error);
    if (error instanceof GraphQLServiceError || error instanceof ServerAccessError) {
      throw error;
    }
    throw new ServerAccessError(`Failed to get server cost info: ${error.message}`);
  }
}

// Export service
export const ServerAccessService = {
  getAvailableServers,
  selectOptimalServer,
  validateServerAccess,
  getServerCostInfo
};
