import { Request, Response } from 'express';
import { gql } from 'graphql-request';
import { v4 as uuidv4 } from 'uuid';
import { nhost } from '../_utils/nhost';

/**
 * Pass Upgrade Function for Unified Credit System
 * Handles upgrading user passes with proper validation and server access updates
 */

interface PassUpgradeRequest {
  userId: string;
  newPassId: string;
}

interface PassUpgradeResponse {
  success: boolean;
  message?: string;
  new_pass_id?: string;
  previous_pass_id?: string;
  error?: string;
}

// GraphQL Queries
const GET_USER_CURRENT_PASS = gql`
  query GetUserCurrentPass($userId: uuid!) {
    user_passes(
      where: {
        user_id: { _eq: $userId },
        status: { _eq: "active" },
        _or: [
          { end_date: { _is_null: true } },
          { end_date: { _gte: "now()" } }
        ]
      }
      order_by: { created_at: desc }
      limit: 1
    ) {
      id
      user_id
      pass_type_id
      status
      pass_type {
        id
        name
        tier
        category
        monthly_price
        is_unlimited
        features
      }
    }
    user_wallet(where: { user_id: { _eq: $userId } }) {
      id
      active_pass_id
    }
  }
`;

const GET_TARGET_PASS_TYPE = gql`
  query GetTargetPassType($passTypeId: uuid!) {
    pass_types_by_pk(id: $passTypeId) {
      id
      name
      tier
      category
      monthly_price
      hours_included
      is_unlimited
      slow_generation_limit
      fast_generation_limit
      features
      is_active
      sort_order
    }
  }
`;

const UPDATE_CURRENT_PASS_STATUS = gql`
  mutation UpdateCurrentPassStatus($passId: uuid!) {
    update_user_passes_by_pk(
      pk_columns: { id: $passId },
      _set: {
        status: "upgraded",
        end_date: "now()",
        updated_at: "now()"
      }
    ) {
      id
      status
      end_date
    }
  }
`;

const CREATE_NEW_PASS = gql`
  mutation CreateNewPass(
    $id: uuid!,
    $userId: uuid!,
    $passTypeId: uuid!,
    $allowedServerTypes: _text!
  ) {
    insert_user_passes_one(
      object: {
        id: $id,
        user_id: $userId,
        pass_type_id: $passTypeId,
        status: "active",
        allowed_server_types: $allowedServerTypes,
        start_date: "now()",
        created_at: "now()",
        updated_at: "now()"
      }
    ) {
      id
      status
      allowed_server_types
      pass_type {
        name
        tier
        category
      }
    }
  }
`;

const UPDATE_WALLET_ACTIVE_PASS = gql`
  mutation UpdateWalletActivePass($walletId: uuid!, $newPassId: uuid!) {
    update_user_wallet_by_pk(
      pk_columns: { id: $walletId },
      _set: {
        active_pass_id: $newPassId,
        updated_at: "now()"
      }
    ) {
      id
      active_pass_id
    }
  }
`;

/**
 * Determine allowed server types based on pass tier and category
 */
function getAllowedServerTypes(tier: string, category: string): string[] {
  if (category === 'Visitor') {
    if (tier === 'Free') {
      return ['slow'];
    } else if (tier === 'Paid') {
      return ['slow', 'fast'];
    }
  } else if (category === 'Citizen') {
    return ['slow', 'fast']; // All citizen tiers get both
  }
  
  return ['slow']; // Default fallback
}

/**
 * Validate pass upgrade
 */
function validatePassUpgrade(currentPass: any, targetPass: any): { valid: boolean; error?: string } {
  if (!currentPass || !targetPass) {
    return { valid: false, error: 'Invalid pass data' };
  }
  
  if (!targetPass.is_active) {
    return { valid: false, error: 'Target pass is not available' };
  }
  
  if (currentPass.pass_type_id === targetPass.id) {
    return { valid: false, error: 'Cannot upgrade to the same pass' };
  }
  
  // Check if it's actually an upgrade (higher tier or different category)
  const currentTier = currentPass.pass_type.tier;
  const targetTier = targetPass.tier;
  const currentCategory = currentPass.pass_type.category;
  const targetCategory = targetPass.category;
  
  // Define tier hierarchy
  const tierHierarchy = { 'Free': 1, 'Paid': 2, 'Lite': 3, 'Pro': 4, 'Elite': 5 };
  const currentRank = tierHierarchy[currentTier] || 0;
  const targetRank = tierHierarchy[targetTier] || 0;
  
  // Allow upgrades within same category or from Visitor to Citizen
  if (currentCategory === targetCategory && targetRank <= currentRank) {
    return { valid: false, error: 'Target pass is not an upgrade' };
  }
  
  if (currentCategory === 'Citizen' && targetCategory === 'Visitor') {
    return { valid: false, error: 'Cannot downgrade from Citizen to Visitor' };
  }
  
  return { valid: true };
}

/**
 * Handle pass upgrade
 */
export const handlePassUpgrade = async (
  req: Request & { body: PassUpgradeRequest },
  res: Response
): Promise<Response | void> => {
  try {
    const { userId, newPassId } = req.body;

    console.log(`[PassUpgrade] 🚀 Processing pass upgrade for user ${userId} to pass ${newPassId}`);

    // Validate input
    if (!userId || !newPassId) {
      return res.status(400).json({
        success: false,
        error: 'User ID and new pass ID are required'
      });
    }

    // Get user's current pass and wallet
    const { data: userData, error: userError } = await nhost.graphql.request(GET_USER_CURRENT_PASS, {
      userId
    });

    if (userError) {
      console.error('[PassUpgrade] ❌ GraphQL error fetching user data:', userError);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch user data'
      });
    }

    if (!userData?.user_passes?.[0] || !userData?.user_wallet?.[0]) {
      return res.status(404).json({
        success: false,
        error: 'User pass or wallet not found'
      });
    }

    const currentPass = userData.user_passes[0];
    const wallet = userData.user_wallet[0];

    // Get target pass type details
    const { data: targetPassData, error: targetPassError } = await nhost.graphql.request(GET_TARGET_PASS_TYPE, {
      passTypeId: newPassId
    });

    if (targetPassError || !targetPassData?.pass_types_by_pk) {
      console.error('[PassUpgrade] ❌ Failed to get target pass type:', targetPassError);
      return res.status(404).json({
        success: false,
        error: 'Target pass type not found'
      });
    }

    const targetPass = targetPassData.pass_types_by_pk;

    console.log(`[PassUpgrade] 📊 Current: ${currentPass.pass_type.category} ${currentPass.pass_type.tier}`);
    console.log(`[PassUpgrade] 📊 Target: ${targetPass.category} ${targetPass.tier}`);

    // Validate upgrade
    const validation = validatePassUpgrade(currentPass, targetPass);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: validation.error
      });
    }

    // Determine allowed server types for new pass
    const allowedServerTypes = getAllowedServerTypes(targetPass.tier, targetPass.category);

    console.log(`[PassUpgrade] 🔄 Upgrading pass with server access: ${allowedServerTypes.join(', ')}`);

    // 1. Update current pass status to upgraded
    const { error: updatePassError } = await nhost.graphql.request(UPDATE_CURRENT_PASS_STATUS, {
      passId: currentPass.id
    });

    if (updatePassError) {
      console.error('[PassUpgrade] ❌ Failed to update current pass status:', updatePassError);
      return res.status(500).json({
        success: false,
        error: 'Failed to update current pass status'
      });
    }

    // 2. Create new pass
    const newUserPassId = uuidv4();
    const { error: createPassError } = await nhost.graphql.request(CREATE_NEW_PASS, {
      id: newUserPassId,
      userId,
      passTypeId: newPassId,
      allowedServerTypes
    });

    if (createPassError) {
      console.error('[PassUpgrade] ❌ Failed to create new pass:', createPassError);
      return res.status(500).json({
        success: false,
        error: 'Failed to create new pass'
      });
    }

    // 3. Update wallet active pass reference
    const { error: updateWalletError } = await nhost.graphql.request(UPDATE_WALLET_ACTIVE_PASS, {
      walletId: wallet.id,
      newPassId: newUserPassId
    });

    if (updateWalletError) {
      console.error('[PassUpgrade] ❌ Failed to update wallet active pass:', updateWalletError);
      // Don't fail the request, just log the error
    }

    console.log(`[PassUpgrade] ✅ Successfully upgraded user ${userId} to ${targetPass.category} ${targetPass.tier}`);

    return res.status(200).json({
      success: true,
      message: `Successfully upgraded to ${targetPass.category} ${targetPass.tier}`,
      new_pass_id: newUserPassId,
      previous_pass_id: currentPass.id
    });

  } catch (error) {
    console.error('[PassUpgrade] 💥 Unexpected error:', error);
    return res.status(500).json({
      success: false,
      error: 'An unexpected error occurred during pass upgrade'
    });
  }
};

/**
 * Express handler for pass upgrade
 */
export default async (req: Request, res: Response): Promise<Response | void> => {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  await handlePassUpgrade(req, res);
};
