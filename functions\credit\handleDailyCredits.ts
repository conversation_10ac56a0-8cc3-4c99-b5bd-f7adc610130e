import { Request, Response } from 'express';
import { gql } from 'graphql-request';
import axios from 'axios';
import {
  CreditTransactionRequest,
  TransactionType,
  UserWallet,
  UserPass,
  PassType,
  CreditErrorType
} from './types';
import { nhost } from '../_utils/nhost';

/**
 * GraphQL query to get user wallet with active pass details
 */
const GET_USER_WALLET_WITH_PASS = gql`
  query GetUserWalletWithPass($userId: uuid!) {
    user_wallet(where: { user_id: { _eq: $userId } }) {
      id
      user_id
      credits_balance
      fast_credits_balance
      last_free_credit_date
      active_pass_id
      created_at
      updated_at
    }
    user_passes(
      where: {
        user_id: { _eq: $userId },
        status: { _eq: "active" },
        end_date: { _gte: "now()" }
      }
    ) {
      id
      user_id
      pass_type_id
      start_date
      end_date
      status
      created_at
      updated_at
      pass_type {
        id
        name
        type
        credits_per_day
        max_hours
        is_unlimited
        price
        features
      }
    }
  }
`;

/**
 * Get the default credits per day for free users
 * @returns The default number of free credits per day
 */
const getDefaultDailyCredits = (): number => {
  return 100; // Default daily credits for free users
};

/**
 * Check if user is eligible for daily credits
 * @param lastFreeCreditDate - The date of the last free credit grant
 * @returns Whether the user is eligible for daily credits
 */
const isEligibleForDailyCredits = (lastFreeCreditDate: string | null): boolean => {
  if (!lastFreeCreditDate) {
    return true;
  }

  const lastDate = new Date(lastFreeCreditDate);

  // Check if the last credit date was yesterday or earlier
  lastDate.setHours(0, 0, 0, 0);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return lastDate < today;
};

/**
 * Handle daily credit distribution
 * @param req - Express request
 * @param res - Express response
 */
export const handleDailyCredits = async (
  req: Request,
  res: Response
): Promise<Response | void> => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required',
        type: CreditErrorType.USER_NOT_FOUND
      });
    }

    // Get user wallet and active passes
    const { data } = await nhost.graphql.request(GET_USER_WALLET_WITH_PASS, {
      userId
    });

    if (!data || !data.user_wallet || data.user_wallet.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User wallet not found',
        type: CreditErrorType.WALLET_NOT_FOUND
      });
    }

    const userWallet: UserWallet = data.user_wallet[0];
    const userPasses: UserPass[] = data.user_passes || [];

    // Check if user is eligible for daily credits
    if (!isEligibleForDailyCredits(userWallet.last_free_credit_date)) {
      return res.status(400).json({
        success: false,
        message: 'User already received daily credits today',
        lastCreditDate: userWallet.last_free_credit_date
      });
    }

    // Determine credits to allocate based on user's pass
    let creditsToAllocate = getDefaultDailyCredits();
    let passName = 'Visitor Pass:Free Tier';

    // Find the active pass with the highest credit allocation
    if (userPasses.length > 0) {
      let highestCreditPass = userPasses.reduce((prev, current) => {
        const prevCredits = prev.pass_type?.credits_per_day || 0;
        const currentCredits = current.pass_type?.credits_per_day || 0;
        return prevCredits > currentCredits ? prev : current;
      });

      if (highestCreditPass.pass_type && highestCreditPass.pass_type.credits_per_day > creditsToAllocate) {
        creditsToAllocate = highestCreditPass.pass_type.credits_per_day;
        passName = highestCreditPass.pass_type.name;
      }
    }

    // Call the credit transaction endpoint to add the credits
    const transactionRequest: CreditTransactionRequest = {
      userId,
      amount: creditsToAllocate,
      transactionType: TransactionType.DAILY_REWARD
    };

    // Use axios to call the handleCreditTransaction endpoint
    const apiEndpoint = process.env.NHOST_FUNCTIONS_URL || 'http://localhost:1337';
    const response = await axios.post(
      `${apiEndpoint}/functions/v1/credit/transaction`,
      transactionRequest,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': req.headers.authorization || ''
        }
      }
    );

    if (response.status === 200 && response.data.success) {
      return res.status(200).json({
        success: true,
        message: `Daily credits (${creditsToAllocate}) added successfully based on ${passName}`,
        creditsAdded: creditsToAllocate,
        passType: passName,
        balanceAfter: response.data.balanceAfter
      });
    } else {
      throw new Error('Failed to process credit transaction');
    }
  } catch (error) {
    console.error('Daily credit distribution error:', error);
    
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
      type: CreditErrorType.DATABASE_ERROR
    });
  }
};

/**
 * Express handler for daily credit distribution
 */
export default async (req: Request, res: Response): Promise<Response | void> => {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  await handleDailyCredits(req, res);
};

