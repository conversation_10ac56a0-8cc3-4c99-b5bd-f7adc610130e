{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "CommonJS",
    "skipLibCheck": true,
    "esModuleInterop": true,

    /* Module Resolution Options */
    "moduleResolution": "node",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* For testing */
    "types": ["jest", "node"],

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,

    /* Additional Options */
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"]
    },
    
    /* Source Map Options */
    "sourceMap": true,
    "declaration": true
  },
  "include": [
    "src/**/*",
    "functions/**/*",
    "**/__tests__/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}

