// src/components/design-system/Layout/Divider.jsx
import React from 'react';
import { Divider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledDivider = styled(MuiDivider)(({ theme, spacing }) => ({
  margin: spacing ? theme.spacing(spacing, 0) : theme.spacing(2, 0),
}));

/**
 * Divider component for section separation
 * 
 * @param {Object} props
 * @param {'horizontal'|'vertical'} props.orientation - Orientation of the divider
 * @param {number} props.spacing - Vertical spacing around divider (in spacing units)
 * @param {string} props.variant - Variant of the divider
 * @param {React.ReactNode} props.children - Text content for the divider
 */
const Divider = ({ 
  orientation = 'horizontal',
  spacing = 2,
  variant = 'fullWidth',
  children,
  ...props 
}) => {
  return (
    <StyledDivider
      orientation={orientation}
      variant={variant}
      spacing={spacing}
      {...props}
    >
      {children}
    </StyledDivider>
  );
};

export default Divider;
