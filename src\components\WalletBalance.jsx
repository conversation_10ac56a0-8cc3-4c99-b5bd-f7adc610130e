// src/components/WalletBalance.jsx
import React from 'react';
import {
  Paper,
  Stack,
  Chip,
  Button,
  Divider,
  Box,
  LinearProgress,
  Typography,
  Tooltip
} from '@mui/material';

// Design System Components
import {
  Text,
  Badge
} from './design-system/index';

// Icons
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import HistoryIcon from '@mui/icons-material/History';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

// Hooks
import { useCreditWallet } from '../hooks/useCreditWallet';
import { formatCreditsToTime } from '../utils/creditCalculations';

/**
 * Enhanced WalletBalance Component for Unified Credit System
 * Displays unified credit balance, time display, and daily credit claiming
 */
function WalletBalance({
  onPurchaseCredits = null,
  showPurchaseButton = true,
  compact = false
}) {
  const {
    wallet,
    loading,
    error,
    formattedBalance,
    usagePercentage,
    canClaimFree,
    claimFreeCredits,
    hasCredits,
    isFreeTier,
    isUnlimited
  } = useCreditWallet();

  const [isClaimingCredits, setIsClaimingCredits] = React.useState(false);

  const handleClaimDailyCredits = async () => {
    try {
      setIsClaimingCredits(true);
      await claimFreeCredits();
    } catch (error) {
      console.error('Failed to claim daily credits:', error);
    } finally {
      setIsClaimingCredits(false);
    }
  };
  if (loading) {
    return (
      <Paper elevation={0} sx={{ p: compact ? 2 : 3, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
        <Box sx={{ width: '100%', my: 2 }}>
          <LinearProgress />
          <Text size="sm" variant="muted" sx={{ mt: 1 }}>Loading wallet information...</Text>
        </Box>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper elevation={0} sx={{ p: compact ? 2 : 3, border: '1px solid', borderColor: 'error.main', borderRadius: 2 }}>
        <Text size="sm" color="error">
          Error loading wallet: {error}
        </Text>
      </Paper>
    );
  }

  if (!wallet) {
    return (
      <Paper elevation={0} sx={{ p: compact ? 2 : 3, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
        <Text size="sm" variant="muted">No wallet information available.</Text>
      </Paper>
    );
  }

  const creditBalance = wallet.credits || 0;
  const timeDisplay = formattedBalance?.timeDisplay || '0s';
  const shortDisplay = formattedBalance?.shortDisplay || '0s';

  if (compact) {
    return (
      <Paper elevation={0} sx={{ p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 2 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Text size="sm" weight={600}>Credits</Text>
          <Tooltip title={`${creditBalance} credits = ${timeDisplay}`}>
            <Chip
              icon={<AccessTimeIcon />}
              label={shortDisplay}
              color={hasCredits ? "primary" : "default"}
              variant="outlined"
              size="small"
            />
          </Tooltip>
        </Stack>
      </Paper>
    );
  }

  return (
    <Paper elevation={0} sx={{ p: 3, border: '1px solid', borderColor: 'divider', borderRadius: 2, height: '100%' }}>
      <Stack spacing={2}>
        {/* Unified Credits Section */}
        <Stack spacing={2}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Text size="md" weight={600}>Credits Balance</Text>
            <Chip
              icon={<AccountBalanceWalletIcon />}
              label={creditBalance}
              color={hasCredits ? "primary" : "default"}
              variant="outlined"
            />
          </Stack>

          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Text size="sm" variant="muted">Time Equivalent</Text>
            <Chip
              icon={<AccessTimeIcon />}
              label={timeDisplay}
              color={hasCredits ? "secondary" : "default"}
              variant="outlined"
              size="small"
            />
          </Stack>

          {/* Usage Percentage for Free Tier */}
          {isFreeTier && usagePercentage > 0 && (
            <Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Daily Usage: {Math.round(usagePercentage)}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={usagePercentage}
                color={usagePercentage > 70 ? "warning" : "primary"}
                sx={{ height: 6, borderRadius: 3 }}
              />
            </Box>
          )}

          {isUnlimited && (
            <Box sx={{ mt: 1 }}>
              <Badge color="success" variant="filled">
                Unlimited Generation Active
              </Badge>
            </Box>
          )}
        </Stack>

        <Divider />

        {/* Action Buttons */}
        <Stack spacing={1}>
          {/* Daily Credits Section for Free Tier */}
          {isFreeTier && (
            canClaimFree ? (
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddCircleOutlineIcon />}
                onClick={handleClaimDailyCredits}
                disabled={isClaimingCredits}
                fullWidth
              >
                {isClaimingCredits ? 'Claiming...' : 'Claim Daily Credits'}
              </Button>
            ) : (
              <Button
                variant="outlined"
                startIcon={<HistoryIcon />}
                disabled
                fullWidth
              >
                Daily Credits Already Claimed
              </Button>
            )
          )}

          {/* Purchase Credits Button */}
          {showPurchaseButton && onPurchaseCredits && (
            <Button
              variant="outlined"
              color="primary"
              startIcon={<ShoppingCartIcon />}
              onClick={onPurchaseCredits}
              fullWidth
            >
              Purchase Credits
            </Button>
          )}
        </Stack>
      </Stack>
    </Paper>
  );
}

export default WalletBalance;
