// src/components/ServerAccessIndicator.jsx
// Component to show available server types and access restrictions

import React from 'react';
import {
  Box,
  Chip,
  Typography,
  Tooltip,
  Alert,
  <PERSON><PERSON>,
  Stack,
  Card,
  CardContent
} from '@mui/material';
import {
  Speed as FastIcon,
  Accessible as SlowIcon,
  Lock as LockIcon,
  Upgrade as UpgradeIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useServerAccess } from '../hooks/useServerAccess';
import { useCreditWallet } from '../hooks/useCreditWallet';

/**
 * ServerAccessIndicator Component
 * Shows available server types, restrictions, and upgrade suggestions
 */
const ServerAccessIndicator = ({ 
  toolId = null, 
  onUpgradeClick = null,
  showDetails = true,
  compact = false 
}) => {
  const { 
    allowedServers, 
    serverRestrictions, 
    canAccessFast, 
    canAccessSlow,
    loading,
    error 
  } = useServerAccess(toolId);
  
  const { wallet, hasCredits } = useCreditWallet();

  if (loading) {
    return (
      <Box sx={{ p: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Loading server access...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load server access: {error}
      </Alert>
    );
  }

  const getServerIcon = (serverType) => {
    return serverType === 'fast' ? <FastIcon /> : <SlowIcon />;
  };

  const getServerColor = (serverType) => {
    if (allowedServers.includes(serverType)) {
      return serverType === 'fast' ? 'primary' : 'success';
    }
    return 'default';
  };

  const getServerLabel = (serverType) => {
    const baseLabel = serverType === 'fast' ? 'Fast Server' : 'Slow Server';
    if (!allowedServers.includes(serverType)) {
      return `${baseLabel} (Locked)`;
    }
    if (serverType === 'fast' && !hasCredits) {
      return `${baseLabel} (No Credits)`;
    }
    return baseLabel;
  };

  const renderServerChip = (serverType) => {
    const isAllowed = allowedServers.includes(serverType);
    const restriction = serverRestrictions[serverType];
    
    const chip = (
      <Chip
        icon={isAllowed ? getServerIcon(serverType) : <LockIcon />}
        label={getServerLabel(serverType)}
        color={getServerColor(serverType)}
        variant={isAllowed ? 'filled' : 'outlined'}
        size={compact ? 'small' : 'medium'}
      />
    );

    if (restriction && showDetails) {
      return (
        <Tooltip 
          title={
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                Access Restricted
              </Typography>
              <Typography variant="body2">
                {restriction.reason}
              </Typography>
              {restriction.suggestedUpgrade && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Suggested: Upgrade to {restriction.suggestedUpgrade.category} {restriction.suggestedUpgrade.tier}
                </Typography>
              )}
            </Box>
          }
          arrow
        >
          {chip}
        </Tooltip>
      );
    }

    return chip;
  };

  const renderUpgradeSuggestion = () => {
    const fastRestriction = serverRestrictions.fast;
    if (!fastRestriction?.suggestedUpgrade || !onUpgradeClick) {
      return null;
    }

    return (
      <Alert 
        severity="info" 
        icon={<UpgradeIcon />}
        action={
          <Button 
            color="inherit" 
            size="small" 
            onClick={() => onUpgradeClick(fastRestriction.suggestedUpgrade)}
          >
            Upgrade
          </Button>
        }
        sx={{ mt: 1 }}
      >
        <Typography variant="body2">
          Upgrade to {fastRestriction.suggestedUpgrade.category} {fastRestriction.suggestedUpgrade.tier} for fast server access
        </Typography>
      </Alert>
    );
  };

  const renderCreditWarning = () => {
    if (canAccessFast && !hasCredits) {
      return (
        <Alert severity="warning" sx={{ mt: 1 }}>
          <Typography variant="body2">
            You have access to fast servers but no credits remaining. 
            {wallet?.isFreeTier && ' Consider claiming your daily free credits or '}
            Purchase credits to use fast servers.
          </Typography>
        </Alert>
      );
    }
    return null;
  };

  if (compact) {
    return (
      <Stack direction="row" spacing={1} alignItems="center">
        {['slow', 'fast'].map(serverType => renderServerChip(serverType))}
        {!canAccessFast && (
          <Tooltip title="Upgrade for fast server access">
            <InfoIcon color="action" fontSize="small" />
          </Tooltip>
        )}
      </Stack>
    );
  }

  return (
    <Card variant="outlined" sx={{ mb: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Server Access
        </Typography>
        
        <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
          {['slow', 'fast'].map(serverType => renderServerChip(serverType))}
        </Stack>

        {showDetails && (
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Available: {allowedServers.join(', ') || 'None'}
            </Typography>
            
            {wallet?.activePass && (
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Current Plan: {wallet.activePass.pass_type.category} {wallet.activePass.pass_type.tier}
              </Typography>
            )}

            {hasCredits && (
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Credits Available: {wallet?.formattedBalance?.timeDisplay || '0s'}
              </Typography>
            )}
          </Box>
        )}

        {renderCreditWarning()}
        {renderUpgradeSuggestion()}
      </CardContent>
    </Card>
  );
};

export default ServerAccessIndicator;
