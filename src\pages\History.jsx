// src/pages/History.jsx
import React, { useState } from 'react';
import {
    Table, TableBody, TableCell, TableHead, TableRow,
    CircularProgress, Paper, TableContainer, Chip, Tooltip,
    Button, Dialog, DialogTitle, DialogContent, DialogActions,
    TextField, FormControl, InputLabel, Select, MenuItem,
    Snackbar, Alert as Mui<PERSON><PERSON>t
} from '@mui/material';
import { useUserId } from '@nhost/react';
import { gql, useQuery, useMutation } from '@apollo/client';

// Design System Components
import {
    Container,
    Stack,
    Heading,
    Text,
    Alert
} from '../components/design-system/index';

// Services
import { GenerationService } from '../services/generationService';

// --- GraphQL Queries ---
const GET_USER_PROMPT_HISTORY_QUERY = gql`
  query GetUserPromptHistory($userId: uuid!) {
    prompts(
      where: { user_id: { _eq: $userId } },
      order_by: { created_at: desc }
    ) {
      id
      user_id
      tool_id
      input_file
      user_prompt
      server_type
      status
      project_id
      error_message
      uploaded_file_id
      created_at
      updated_at
    }
  }
`;

const GET_USER_PROJECTS_QUERY = gql`
  query GetUserProjects($userId: uuid!) {
    projects(
      where: { user_id: { _eq: $userId } },
      order_by: { created_at: desc }
    ) {
      id
      name
      created_at
    }
  }
`;

const CREATE_PROJECT_MUTATION = gql`
  mutation CreateProject($userId: uuid!, $name: String!, $promptId: uuid!) {
    insert_projects_one(object: {
      user_id: $userId,
      name: $name,
      prompt_id: $promptId
    }) {
      id
      name
      created_at
    }
  }
`;

const statusColors = {
    pending: 'default',
    processing: 'info',
    completed: 'success',
    failed: 'error',
    credit_error: 'error',
};

function History() {
    const userId = useUserId();

    // State for Save to Project dialog
    const [saveDialogOpen, setSaveDialogOpen] = useState(false);
    const [selectedPrompt, setSelectedPrompt] = useState(null);
    const [newProjectName, setNewProjectName] = useState('');
    const [selectedProjectId, setSelectedProjectId] = useState('');
    const [isCreatingNewProject, setIsCreatingNewProject] = useState(true);
    const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });

    // Queries and mutations
    const { loading, error, data, refetch } = useQuery(GET_USER_PROMPT_HISTORY_QUERY, {
        variables: { userId },
        skip: !userId,
        fetchPolicy: 'cache-and-network',
    });

    const { data: projectsData } = useQuery(GET_USER_PROJECTS_QUERY, {
        variables: { userId },
        skip: !userId,
        fetchPolicy: 'cache-and-network',
    });

    const [createProject] = useMutation(CREATE_PROJECT_MUTATION);

    // Handle Save to Project
    const handleSaveToProject = (prompt) => {
        setSelectedPrompt(prompt);
        setNewProjectName('');
        setSelectedProjectId('');
        setIsCreatingNewProject(true);
        setSaveDialogOpen(true);
    };

    const handleSaveProject = async () => {
        if (!selectedPrompt) return;

        try {
            let projectId = selectedProjectId;

            // Create new project if needed
            if (isCreatingNewProject) {
                if (!newProjectName.trim()) {
                    setToast({ open: true, message: 'Please enter a project name', severity: 'error' });
                    return;
                }

                console.log('[History] Creating new project with GenerationService patterns...');
                const { data: newProjectData } = await createProject({
                    variables: {
                        userId,
                        name: newProjectName.trim(),
                        promptId: selectedPrompt.id
                    }
                });

                projectId = newProjectData?.insert_projects_one?.id;
                if (!projectId) {
                    throw new Error('Failed to create project');
                }
                console.log('[History] ✅ Project created successfully:', projectId);
            }

            // Update prompt with project ID using centralized GenerationService
            console.log('[History] 🔗 Linking prompt to project using GenerationService...');
            const success = await GenerationService.updatePromptProject(selectedPrompt.id, projectId);

            if (!success) {
                throw new Error('Failed to link prompt to project');
            }

            // Refresh data to show updated state
            await refetch();

            console.log('[History] ✅ Prompt successfully linked to project');
            setToast({
                open: true,
                message: `Prompt saved to project successfully!`,
                severity: 'success'
            });
            setSaveDialogOpen(false);

        } catch (error) {
            console.error('[History] ❌ Error saving to project:', error);
            setToast({
                open: true,
                message: `Failed to save to project: ${error.message}`,
                severity: 'error'
            });
        }
    };

    if (loading) {
        return (
            <Container>
                <Stack justifyContent="center" alignItems="center" sx={{ p: 5 }}>
                    <CircularProgress />
                    <Text>Loading your prompt history...</Text>
                </Stack>
            </Container>
        );
    }

    if (error) {
        return (
            <Container sx={{ py: 4 }}>
                <Alert severity="error">Error loading history: {error.message}</Alert>
            </Container>
        );
    }

    if (!userId && !loading) {
        return (
            <Container sx={{ py: 4 }}>
                <Alert severity="warning">Please log in to view history.</Alert>
            </Container>
        );
    }

    const prompts = data?.prompts || [];
    const projects = projectsData?.projects || [];

    return (
        <Container maxWidth="1280px" sx={{ py: 4 }}>
            <Stack spacing={4}>
                <Heading level="h1" size="4xl">
                    Generation History
                </Heading>

                {prompts.length === 0 ? (
                    <Paper sx={{ p: 3, textAlign: 'center' }}>
                        <Text variant="muted">No generation history found.</Text>
                    </Paper>
                ) : (
                <TableContainer
                    component={Paper}
                    sx={{
                        // Enable horizontal scrolling
                        overflowX: 'auto',
                        // Ensure table maintains minimum width for readability
                        '& .MuiTable-root': {
                            minWidth: 1000, // Minimum width to trigger horizontal scroll
                        },
                        // Smooth scrolling on mobile
                        WebkitOverflowScrolling: 'touch',
                        // Custom scrollbar styling
                        '&::-webkit-scrollbar': {
                            height: 8,
                        },
                        '&::-webkit-scrollbar-track': {
                            backgroundColor: 'rgba(0,0,0,0.1)',
                            borderRadius: 4,
                        },
                        '&::-webkit-scrollbar-thumb': {
                            backgroundColor: 'rgba(0,0,0,0.3)',
                            borderRadius: 4,
                            '&:hover': {
                                backgroundColor: 'rgba(0,0,0,0.5)',
                            },
                        },
                    }}
                >
                    <Table stickyHeader aria-label="User prompt history table">
                        <TableHead>
                            <TableRow>
                                <TableCell sx={{fontWeight: 'bold', minWidth: 140}}>Date</TableCell>
                                <TableCell sx={{fontWeight: 'bold', minWidth: 100}}>Tool</TableCell>
                                <TableCell sx={{fontWeight: 'bold', minWidth: 200}}>Input File</TableCell>
                                <TableCell sx={{fontWeight: 'bold', minWidth: 200}}>User Prompt</TableCell>
                                <TableCell sx={{fontWeight: 'bold', minWidth: 100}}>Server Type</TableCell>
                                <TableCell sx={{fontWeight: 'bold', minWidth: 100}}>Status</TableCell>
                                <TableCell sx={{fontWeight: 'bold', minWidth: 100}}>Error</TableCell>
                                <TableCell sx={{fontWeight: 'bold', minWidth: 140}}>Actions</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {prompts.map((prompt) => (
                                <TableRow hover key={prompt.id}>
                                    <TableCell>
                                        <Tooltip title={new Date(prompt.created_at).toISOString()}>
                                            <span>{new Date(prompt.created_at).toLocaleString()}</span>
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>{prompt.tool_id || 'N/A'}</TableCell>
                                    <TableCell>
                                        <Tooltip title={prompt.input_file || "No input file"}>
                                            <Text
                                                size="sm"
                                                sx={{
                                                    maxWidth: 200,
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'nowrap',
                                                    display: 'block'
                                                }}
                                            >
                                                {prompt.input_file || "No file"}
                                            </Text>
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>
                                        <Tooltip title={prompt.user_prompt || "No user prompt"}>
                                            <Text
                                                size="sm"
                                                sx={{
                                                    maxWidth: 200,
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'nowrap',
                                                    display: 'block'
                                                }}
                                            >
                                                {prompt.user_prompt || "No prompt"}
                                            </Text>
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>{prompt.server_type || '-'}</TableCell>
                                    <TableCell>
                                        <Chip
                                            label={prompt.status || 'Unknown'}
                                            size="small"
                                            color={statusColors[prompt.status] || 'default'}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        {prompt.error_message ? (
                                            <Tooltip title={prompt.error_message}>
                                                <Chip
                                                    label="Error"
                                                    color="error"
                                                    size="small"
                                                />
                                            </Tooltip>
                                        ) : (
                                            'None'
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        {prompt.status === 'completed' && !prompt.project_id && (
                                            <Button
                                                size="small"
                                                variant="outlined"
                                                onClick={() => handleSaveToProject(prompt)}
                                            >
                                                Save to Project
                                            </Button>
                                        )}
                                        {prompt.project_id && (
                                            <Chip
                                                label="Saved"
                                                color="success"
                                                size="small"
                                            />
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
                )}
            </Stack>

            {/* Save to Project Dialog */}
            <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)} maxWidth="sm" fullWidth>
                <DialogTitle>Save to Project</DialogTitle>
                <DialogContent>
                    <Stack spacing={3} sx={{ mt: 1 }}>
                        <FormControl fullWidth>
                            <InputLabel>Project Option</InputLabel>
                            <Select
                                value={isCreatingNewProject ? 'new' : 'existing'}
                                onChange={(e) => setIsCreatingNewProject(e.target.value === 'new')}
                                label="Project Option"
                            >
                                <MenuItem value="new">Create New Project</MenuItem>
                                {projects.length > 0 && (
                                    <MenuItem value="existing">Use Existing Project</MenuItem>
                                )}
                            </Select>
                        </FormControl>

                        {isCreatingNewProject ? (
                            <TextField
                                fullWidth
                                label="Project Name"
                                value={newProjectName}
                                onChange={(e) => setNewProjectName(e.target.value)}
                                placeholder="Enter project name"
                            />
                        ) : (
                            <FormControl fullWidth>
                                <InputLabel>Select Project</InputLabel>
                                <Select
                                    value={selectedProjectId}
                                    onChange={(e) => setSelectedProjectId(e.target.value)}
                                    label="Select Project"
                                >
                                    {projects.map((project) => (
                                        <MenuItem key={project.id} value={project.id}>
                                            {project.name}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        )}
                    </Stack>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setSaveDialogOpen(false)}>Cancel</Button>
                    <Button
                        onClick={handleSaveProject}
                        variant="contained"
                        disabled={isCreatingNewProject ? !newProjectName.trim() : !selectedProjectId}
                    >
                        Save
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Toast Notifications */}
            <Snackbar
                open={toast.open}
                autoHideDuration={6000}
                onClose={() => setToast({ ...toast, open: false })}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
                <MuiAlert
                    onClose={() => setToast({ ...toast, open: false })}
                    severity={toast.severity}
                    sx={{ width: '100%' }}
                >
                    {toast.message}
                </MuiAlert>
            </Snackbar>
        </Container>
    );
}

export default History;