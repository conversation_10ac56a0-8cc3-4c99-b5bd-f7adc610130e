import { Request, Response } from 'express';

/**
 * Enum for different types of credit transactions
 */
export enum TransactionType {
  CREDIT_PURCHASE = 'credit_purchase',
  CREDIT_USAGE = 'credit_usage',
  DAILY_REWARD = 'daily_reward',
  SUBSCRIPTION_CREDIT = 'subscription_credit',
  REFUND = 'refund',
  PROMO_CREDIT = 'promo_credit'
}

/**
 * Enum for different credit deduction sources
 */
export enum DeductionSource {
  REGULAR = 'regular',
  FAST = 'fast',
  UNLIMITED = 'unlimited'
}

/**
 * Interface for credit transaction request
 */
export interface CreditTransactionRequest {
  userId: string;
  amount: number;
  transactionType: TransactionType;
  referenceId?: string; // For linking to prompts or purchases
  bundleId?: string; // For credit purchases
  deductionSource?: DeductionSource; // For credit usage
}

/**
 * Interface for wallet data (Updated for unified credit system)
 */
export interface UserWallet {
  id: string;
  user_id: string;
  credits_balance: number;
  last_free_credit_claim: string | null;
  daily_free_credits_granted: number;
  total_credits_purchased: number;
  active_pass_id: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for user pass data
 */
export interface UserPass {
  id: string;
  user_id: string;
  pass_type_id: string;
  start_date: string;
  end_date: string;
  status: 'active' | 'expired' | 'cancelled';
  created_at: string;
  updated_at: string;
  pass_type?: PassType;
}

/**
 * Interface for pass type data
 */
export interface PassType {
  id: string;
  name: string;
  type: 'free' | 'paid' | 'subscription';
  credits_per_day: number;
  max_hours: number;
  is_unlimited: boolean;
  price: number;
  features: any;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for credit transaction response
 */
export interface CreditTransactionResponse {
  success: boolean;
  message: string;
  transactionId?: string;
  balanceAfter?: number;
  fastBalanceAfter?: number;
  hasUnlimitedUsage?: boolean;
}

/**
 * Interface for credit bundle data
 */
export interface CreditBundle {
  id: string;
  name: string;
  credits: number;
  price: number;
  duration_days: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for credit transaction data
 */
export interface CreditTransaction {
  id: string;
  user_id: string;
  amount: number;
  transaction_type: string;
  balance_after: number;
  reference_id?: string;
  bundle_id?: string;
  created_at: string;
}

/**
 * Type for Express request handler with credit transaction
 */
export type CreditTransactionHandler = (
  req: Request & { body: CreditTransactionRequest },
  res: Response
) => Promise<void>;

/**
 * Error types for credit transactions
 */
export enum CreditErrorType {
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  INVALID_TRANSACTION_TYPE = 'INVALID_TRANSACTION_TYPE',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  WALLET_NOT_FOUND = 'WALLET_NOT_FOUND',
  DATABASE_ERROR = 'DATABASE_ERROR',
  PASS_EXPIRED = 'PASS_EXPIRED',
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  BUNDLE_NOT_FOUND = 'BUNDLE_NOT_FOUND'
}

/**
 * Credit transaction error class
 */
export class CreditError extends Error {
  type: CreditErrorType;
  statusCode: number;

  constructor(type: CreditErrorType, message: string, statusCode: number = 400) {
    super(message);
    this.type = type;
    this.statusCode = statusCode;
    this.name = 'CreditError';
  }
}

