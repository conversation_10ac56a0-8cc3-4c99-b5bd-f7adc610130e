// src/components/design-system/Feedback/Loading.jsx
import React from 'react';
import { CircularProgress, LinearProgress, Skeleton, Box } from '@mui/material';
import { styled } from '@mui/material/styles';

const LoadingContainer = styled(Box)(({ theme, centered }) => ({
  ...(centered && {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '200px',
  }),
}));

/**
 * Loading component for various loading states
 * 
 * @param {Object} props
 * @param {'spinner'|'linear'|'skeleton'} props.type - Loading type
 * @param {'small'|'medium'|'large'} props.size - Loading size
 * @param {boolean} props.centered - Whether to center the loading indicator
 * @param {string} props.text - Loading text
 * @param {React.ReactNode} props.children - Custom loading content
 */
const Loading = ({ 
  type = 'spinner',
  size = 'medium',
  centered = false,
  text,
  children,
  ...props 
}) => {
  const sizeMap = {
    small: 20,
    medium: 40,
    large: 60,
  };

  if (children) {
    return (
      <LoadingContainer centered={centered} {...props}>
        {children}
      </LoadingContainer>
    );
  }

  if (type === 'linear') {
    return (
      <Box sx={{ width: '100%' }} {...props}>
        <LinearProgress />
        {text && (
          <Box sx={{ mt: 1, textAlign: 'center' }}>
            {text}
          </Box>
        )}
      </Box>
    );
  }

  if (type === 'skeleton') {
    return (
      <Box {...props}>
        <Skeleton variant="rectangular" width="100%" height={sizeMap[size]} />
        {text && (
          <Skeleton variant="text" width="60%" sx={{ mt: 1 }} />
        )}
      </Box>
    );
  }

  return (
    <LoadingContainer centered={centered} {...props}>
      <Box sx={{ textAlign: 'center' }}>
        <CircularProgress size={sizeMap[size]} />
        {text && (
          <Box sx={{ mt: 1 }}>
            {text}
          </Box>
        )}
      </Box>
    </LoadingContainer>
  );
};

/**
 * LoadingSkeleton component for content placeholders
 */
const LoadingSkeleton = ({ 
  lines = 3,
  avatar = false,
  width = '100%',
  height = 20,
  ...props 
}) => {
  return (
    <Box {...props}>
      {avatar && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Skeleton variant="circular" width={40} height={40} />
          <Box sx={{ ml: 2, flex: 1 }}>
            <Skeleton variant="text" width="30%" />
            <Skeleton variant="text" width="20%" />
          </Box>
        </Box>
      )}
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          width={index === lines - 1 ? '60%' : width}
          height={height}
          sx={{ mb: 0.5 }}
        />
      ))}
    </Box>
  );
};

Loading.Skeleton = LoadingSkeleton;

export default Loading;
