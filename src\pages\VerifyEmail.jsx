// src/pages/VerifyEmail.jsx
import React, { useState, useEffect, useCallback } from 'react';
import {
  Container, Typography, Button, Box, Alert, CircularProgress, Paper,
  Link as MuiLink, LinearProgress, Divider, Chip, Tooltip
} from '@mui/material';
import MarkEmailReadIcon from '@mui/icons-material/MarkEmailRead';
import RefreshIcon from '@mui/icons-material/Refresh';
import TimerIcon from '@mui/icons-material/Timer';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useSignOut, useUserData, useAuthenticationStatus, useSignInEmailPasswordless } from '@nhost/react';
import { nhost } from '../services/nhost';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';

function VerifyEmail() {
  const { signOut, isSigningOut } = useSignOut();
  const { isAuthenticated, isLoading } = useAuthenticationStatus();
  const userData = useUserData();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  // Use Nhost hook for resending verification emails
  const { signInEmailPasswordless, isLoading: isResendLoading } = useSignInEmailPasswordless();

  // Get email from multiple sources with priority order
  const userEmail = userData?.email ||
    localStorage.getItem('pendingVerificationEmail') ||
    sessionStorage.getItem('registrationEmail') ||
    sessionStorage.getItem('pendingVerificationEmail') ||
    localStorage.getItem('rememberedEmail');

  // Check for verification token in URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const verificationToken = urlParams.get('token') || urlParams.get('verification_token');
  const [tokenProcessed, setTokenProcessed] = useState(false);

  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [lastChecked, setLastChecked] = useState(Date.now());
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);

  // Log component state for debugging
  console.log("VerifyEmail Component - Render State:", {
    isAuthenticated,
    isLoading,
    userEmail,
    emailVerified: nhost.auth.getUser()?.emailVerified,
    countdown,
    autoRefreshEnabled,
    verificationToken,
    tokenProcessed,
    success,
    error,
    pendingEmail: localStorage.getItem('pendingVerificationEmail'),
    sessionEmail: sessionStorage.getItem('registrationEmail'),
    userData: userData?.email,
    currentUrl: window.location.href
  });

  // Function to manually check verification status
  const handleCheckVerificationStatus = useCallback(async () => {
    setCheckingStatus(true);
    try {
      await nhost.auth.refreshSession();
      setLastChecked(Date.now());

      // Check if email is verified after refresh
      const user = nhost.auth.getUser();
      if (user?.emailVerified) {
        setSuccess('Email verified successfully! Redirecting to dashboard...');
        enqueueSnackbar('✅ Email verified successfully! Welcome!', {
          variant: 'success',
          autoHideDuration: 3000,
          anchorOrigin: { vertical: 'top', horizontal: 'center' }
        });

        // Clear pending verification email
        localStorage.removeItem('pendingVerificationEmail');
        sessionStorage.removeItem('pendingVerificationEmail');
        sessionStorage.removeItem('registrationEmail');

        // Redirect after a short delay
        setTimeout(() => {
          const intendedDestination = localStorage.getItem('postVerificationRedirect') || '/dashboard';
          localStorage.removeItem('postVerificationRedirect');
          navigate(intendedDestination, { replace: true });
        }, 2000);
      } else {
        enqueueSnackbar('📧 Email not verified yet. Please check your inbox and click the verification link.', {
          variant: 'info',
          autoHideDuration: 5000,
          anchorOrigin: { vertical: 'top', horizontal: 'center' }
        });
      }
    } catch (err) {
      console.error("Error checking verification status:", err);
      enqueueSnackbar('❌ Failed to check verification status. Please try again.', {
        variant: 'error',
        autoHideDuration: 5000,
        anchorOrigin: { vertical: 'top', horizontal: 'center' }
      });
    } finally {
      setCheckingStatus(false);
    }
  }, [enqueueSnackbar, navigate]);

  // Handle resending verification email with countdown
  const handleResendVerification = async () => {
    if (countdown > 0 || !userEmail) return; // Prevent resending if countdown is active or no email

    setError('');
    setSuccess('');

    try {
      // Use Nhost hook to resend verification email
      await signInEmailPasswordless(userEmail);

      // Set success message and start countdown
      setSuccess('Verification email sent successfully! Please check your inbox (and spam folder).');
      enqueueSnackbar('✅ Verification email sent! Check your inbox and spam folder.', {
        variant: 'success',
        autoHideDuration: 5000,
        anchorOrigin: { vertical: 'top', horizontal: 'center' }
      });

      // Start 60-second countdown to prevent spam
      setCountdown(60);
    } catch (err) {
      console.error("Error resending verification email:", err);
      const errorMessage = err.message || 'Failed to resend verification email.';
      setError(errorMessage);
      enqueueSnackbar(`❌ ${errorMessage}`, {
        variant: 'error',
        autoHideDuration: 6000,
        anchorOrigin: { vertical: 'top', horizontal: 'center' }
      });
    }
  };

  // Countdown timer effect
  useEffect(() => {
    if (countdown <= 0) return;

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown]);

  // Handle email verification token from URL
  useEffect(() => {
    if (verificationToken && !tokenProcessed) {
      console.log('Processing verification token from URL:', verificationToken);
      setTokenProcessed(true);

      // Process the verification token
      const processVerificationToken = async () => {
        try {
          // Use Nhost's email verification method
          // The verification happens automatically when the user clicks the link
          // We just need to refresh the session to get the updated user data
          await nhost.auth.refreshSession();

          // Check if user is now verified
          const currentUser = nhost.auth.getUser();

          if (currentUser?.emailVerified) {
            console.log('Email verification successful');
            setSuccess('Email verified successfully! Redirecting to dashboard...');
            enqueueSnackbar('✅ Email verified successfully! Welcome!', {
              variant: 'success',
              autoHideDuration: 3000,
              anchorOrigin: { vertical: 'top', horizontal: 'center' }
            });

            // Clear verification data
            localStorage.removeItem('pendingVerificationEmail');
            sessionStorage.removeItem('pendingVerificationEmail');
            sessionStorage.removeItem('registrationEmail');

            // Redirect after a short delay
            setTimeout(() => {
              const intendedDestination = localStorage.getItem('postVerificationRedirect') || '/dashboard';
              localStorage.removeItem('postVerificationRedirect');
              navigate(intendedDestination, { replace: true });
            }, 2000);
          } else {
            console.log('Email verification token processed but user not verified yet');
            setError('Email verification is being processed. Please wait a moment and try refreshing.');
            enqueueSnackbar('📧 Verification in progress. Please wait a moment.', {
              variant: 'info',
              autoHideDuration: 5000,
              anchorOrigin: { vertical: 'top', horizontal: 'center' }
            });
          }
        } catch (error) {
          console.error('Error processing verification token:', error);
          setError('An error occurred during email verification. Please try again.');
          enqueueSnackbar('❌ Verification error. Please try again.', {
            variant: 'error',
            autoHideDuration: 6000,
            anchorOrigin: { vertical: 'top', horizontal: 'center' }
          });
        }
      };

      processVerificationToken();
    }
  }, [verificationToken, tokenProcessed, navigate, enqueueSnackbar]);

  // Main effect for authentication and verification status
  useEffect(() => {
    // 1. Handle unauthentication: Redirect to login if the user logs out while on this page
    if (!isLoading && !isAuthenticated) {
      console.log('User not authenticated on VerifyEmail page, redirecting to login');
      navigate('/login', { replace: true });
      return; // Stop further execution in this effect run
    }

    // 2. Check if user is already verified and redirect
    if (!isLoading && isAuthenticated && userData?.emailVerified) {
      console.log('User is already verified, redirecting to dashboard');
      const intendedDestination = localStorage.getItem('postVerificationRedirect') || '/dashboard';
      localStorage.removeItem('postVerificationRedirect');
      // Clear verification emails
      localStorage.removeItem('pendingVerificationEmail');
      sessionStorage.removeItem('pendingVerificationEmail');
      sessionStorage.removeItem('registrationEmail');
      navigate(intendedDestination, { replace: true });
      return;
    }

    // 3. Periodically refresh session to check for verification status updates
    let interval;
    if (autoRefreshEnabled && isAuthenticated && !verificationToken) {
      interval = setInterval(async () => {
        console.log("VerifyEmail: Auto-refreshing session to check status...");
        setLastChecked(Date.now());

        try {
          // Refresh session and check verification status
          await nhost.auth.refreshSession();

          // Check if user is now verified
          const currentUser = nhost.auth.getUser();
          if (currentUser?.emailVerified) {
            console.log('User verification detected during auto-refresh');
            setSuccess('Email verified successfully! Redirecting to dashboard...');
            enqueueSnackbar('✅ Email verified successfully! Welcome!', {
              variant: 'success',
              autoHideDuration: 3000,
              anchorOrigin: { vertical: 'top', horizontal: 'center' }
            });

            // Clear verification data
            localStorage.removeItem('pendingVerificationEmail');
            sessionStorage.removeItem('pendingVerificationEmail');
            sessionStorage.removeItem('registrationEmail');

            // Redirect after a short delay
            setTimeout(() => {
              const intendedDestination = localStorage.getItem('postVerificationRedirect') || '/dashboard';
              localStorage.removeItem('postVerificationRedirect');
              navigate(intendedDestination, { replace: true });
            }, 2000);
          }
        } catch (error) {
          console.error('Error during auto-refresh:', error);
        }
      }, 10000); // Check every 10 seconds
    }

    // Cleanup interval on component unmount
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isAuthenticated, isLoading, navigate, autoRefreshEnabled, userData, verificationToken, enqueueSnackbar]); // Dependencies


  // Format time for last checked display
  const formatLastChecked = () => {
    const now = Date.now();
    const diff = now - lastChecked;

    if (diff < 60000) { // Less than a minute
      return 'just now';
    } else if (diff < 3600000) { // Less than an hour
      const minutes = Math.floor(diff / 60000);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else {
      const hours = Math.floor(diff / 3600000);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    }
  };

  return (
    <Container component="main" maxWidth="sm" sx={{ mt: { xs: 6, sm: 8 }, mb: 4 }}>
      <Paper elevation={3} sx={{ p: { xs: 3, sm: 4 }, display: 'flex', flexDirection: 'column', alignItems: 'center', borderRadius: 2 }}>
        <MarkEmailReadIcon sx={{ fontSize: {xs: 50, sm: 60}, color: 'primary.main', mb: 2 }} />

        <Typography component="h1" variant="h4" align="center" gutterBottom sx={{ mb: 1 }}>
          Verify Your Email
        </Typography>

        {/* Status Chip */}
        <Chip
          icon={verificationToken ? <CircularProgress size={16} color="inherit" /> :
                success ? <CheckCircleIcon /> :
                <InfoOutlinedIcon />}
          label={verificationToken ? "Processing verification..." :
                success ? "Email verified!" :
                "Waiting for verification"}
          color={success ? "success" : verificationToken ? "warning" : "primary"}
          variant="outlined"
          sx={{ mb: 2 }}
        />

        <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 2 }}>
          {verificationToken ? (
            <>
              Processing your email verification...
              <br />
              <Box component="strong" sx={{ fontWeight: 'medium', my: 1, display: 'inline-block', wordBreak: 'break-all' }}>
                {userEmail || 'your email address'}
              </Box>
              <br />
              Please wait while we verify your email address.
            </>
          ) : success ? (
            <>
              Email verification successful!
              <br />
              <Box component="strong" sx={{ fontWeight: 'medium', my: 1, display: 'inline-block', wordBreak: 'break-all' }}>
                {userEmail || 'your email address'}
              </Box>
              <br />
              You will be redirected to the dashboard shortly.
            </>
          ) : (
            <>
              Thank you for registering! A verification link has been sent to:
              <br />
              <Box component="strong" sx={{ fontWeight: 'medium', my: 1, display: 'inline-block', wordBreak: 'break-all' }}>
                {userEmail || 'your email address'}
              </Box>
              <br />
              Please click the link in the email to activate your account.
            </>
          )}
        </Typography>

        {/* Show warning if no email is found */}
        {!userEmail && (
          <Alert severity="warning" sx={{ width: '100%', mb: 2 }}>
            <Typography variant="body2">
              We couldn't retrieve your email address. If you just registered, please try logging in again or contact support.
            </Typography>
          </Alert>
        )}

        {/* Tips Box */}
        <Box sx={{
          bgcolor: 'background.default',
          p: 2,
          borderRadius: 1,
          width: '100%',
          mb: 3,
          border: '1px dashed',
          borderColor: 'divider'
        }}>
          <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
            <InfoOutlinedIcon fontSize="small" sx={{ mr: 0.5 }} />
            Tips:
          </Typography>
          <Typography variant="body2" component="ul" sx={{ pl: 2, m: 0 }}>
            <li>Check your spam/junk folder</li>
            <li>Make sure you entered the correct email</li>
            <li>The verification link expires after 24 hours</li>
            <li>Click "Check Status" after verifying your email</li>
          </Typography>
        </Box>

        {/* Error and Success Alerts */}
        {error && (
          <Alert
            severity="error"
            sx={{ width: '100%', mb: 2.5 }}
            onClose={() => setError('')}
            icon={<ErrorOutlineIcon />}
          >
            {error}
          </Alert>
        )}

        {success && (
          <Alert
            severity="success"
            sx={{ width: '100%', mb: 2.5 }}
            onClose={() => setSuccess('')}
            icon={<CheckCircleIcon />}
          >
            {success}
          </Alert>
        )}

        {/* Auto-refresh Status */}
        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="caption" color="text.secondary">
            Last checked: {formatLastChecked()}
          </Typography>
          <Button
            size="small"
            startIcon={<RefreshIcon />}
            onClick={handleCheckVerificationStatus}
            disabled={checkingStatus}
            color="primary"
            variant="text"
          >
            {checkingStatus ? 'Checking...' : 'Check Status'}
          </Button>
        </Box>

        {/* Auto-refresh Progress */}
        {autoRefreshEnabled && (
          <Box sx={{ width: '100%', mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="caption" color="text.secondary">
                Auto-refreshing every 10 seconds
              </Typography>
              <Button
                size="small"
                onClick={() => setAutoRefreshEnabled(false)}
                color="primary"
                variant="text"
                sx={{ minWidth: 'auto', p: 0.5 }}
              >
                Disable
              </Button>
            </Box>
            <LinearProgress variant="indeterminate" sx={{ height: 4, borderRadius: 1 }} />
          </Box>
        )}

        {!autoRefreshEnabled && (
          <Box sx={{ width: '100%', mb: 2 }}>
            <Button
              size="small"
              onClick={() => setAutoRefreshEnabled(true)}
              color="primary"
              variant="text"
              fullWidth
            >
              Enable Auto-refresh
            </Button>
          </Box>
        )}

        <Divider sx={{ width: '100%', my: 2 }} />

        {/* Action Buttons */}
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1.5 }}>
          {/* Resend Button with Countdown */}
          <Button
            variant="contained"
            fullWidth
            onClick={handleResendVerification}
            disabled={isResendLoading || !userEmail || countdown > 0}
            startIcon={isResendLoading ? <CircularProgress size={20} color="inherit" /> : <MarkEmailReadIcon />}
            sx={{ py: 1.2 }}
          >
            {isResendLoading ? 'Sending...' :
              countdown > 0 ? `Resend Available in ${countdown}s` : 'Resend Verification Email'}
          </Button>

          {countdown > 0 && (
            <LinearProgress
              variant="determinate"
              value={(60 - countdown) / 60 * 100}
              sx={{ height: 4, borderRadius: 1, mb: 1 }}
            />
          )}

          {/* Logout Button */}
          <Button
            variant="outlined"
            fullWidth
            onClick={signOut}
            disabled={isSigningOut}
            sx={{ py: 1.2 }}
          >
            {isSigningOut ? 'Logging out...' : 'Log Out'}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
}

export default VerifyEmail;
